#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para calcular métricas de análise por destinatário
- Meses desde última fatura
- Número total de faturas
- Valor total faturado
- Valor médio por fatura
- Análises complementares

Autor: Análise de Dados LegalOne
Data: 2025-01-07
"""

import pandas as pd
import numpy as np
from datetime import datetime, date
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

def carregar_dados_limpos():
    """
    Carrega os dados já limpos
    """
    print("📂 CARREGANDO DADOS LIMPOS")
    print("="*50)
    
    pasta_data = Path('data')
    arquivo_faturas = pasta_data / 'faturas_limpa.xlsx'
    
    if not arquivo_faturas.exists():
        print("❌ Arquivo faturas_limpa.xlsx não encontrado!")
        print("   Execute primeiro o script limpar_dados.py")
        return None
    
    try:
        df_faturas = pd.read_excel(arquivo_faturas)
        print(f"✅ Faturas carregadas: {len(df_faturas)} registros")
        print(f"   Período: {df_faturas['Emitida em'].min()} até {df_faturas['Emitida em'].max()}")
        
        return df_faturas
        
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {str(e)}")
        return None

def calcular_metricas_por_destinatario(df_faturas):
    """
    Calcula todas as métricas por destinatário
    """
    print(f"\n📊 CALCULANDO MÉTRICAS POR DESTINATÁRIO")
    print("="*60)
    
    # Data de referência (data atual)
    data_referencia = datetime.now()
    print(f"📅 Data de referência: {data_referencia.strftime('%d/%m/%Y')}")
    
    # Filtrar apenas registros com data válida e destinatário válido
    df_valido = df_faturas[
        (df_faturas['Emitida em'].notna()) & 
        (df_faturas['Destinatário da fatura'].notna()) &
        (df_faturas['Destinatário da fatura'] != '')
    ].copy()
    
    print(f"📋 Registros válidos para análise: {len(df_valido)} de {len(df_faturas)}")
    
    # Agrupar por destinatário
    print("\n🔄 Processando métricas...")
    
    metricas_list = []
    
    destinatarios_unicos = df_valido['Destinatário da fatura'].unique()
    total_destinatarios = len(destinatarios_unicos)
    
    for i, destinatario in enumerate(destinatarios_unicos, 1):
        if i % 50 == 0 or i == total_destinatarios:
            print(f"   Processando {i}/{total_destinatarios} destinatários...")
        
        # Filtrar faturas do destinatário
        faturas_dest = df_valido[df_valido['Destinatário da fatura'] == destinatario].copy()
        
        # 1. MESES DESDE ÚLTIMA FATURA
        data_mais_recente = faturas_dest['Emitida em'].max()
        dias_desde_ultima = (data_referencia - data_mais_recente).days
        meses_desde_ultima = round(dias_desde_ultima / 30.44, 1)  # 30.44 = média de dias por mês
        
        # 2. NÚMERO TOTAL DE FATURAS
        # Contar faturas únicas (por número da fatura)
        faturas_unicas = faturas_dest['Nº da fatura'].nunique()
        total_registros = len(faturas_dest)  # Total de linhas (pode haver duplicatas)
        
        # 3. VALOR TOTAL FATURADO
        valor_total = faturas_dest['Soma dos itens'].sum()
        
        # 4. VALOR MÉDIO POR FATURA
        valor_medio = valor_total / faturas_unicas if faturas_unicas > 0 else 0
        
        # 5. INFORMAÇÕES ADICIONAIS
        primeira_fatura = faturas_dest['Emitida em'].min()
        periodo_cliente_dias = (data_mais_recente - primeira_fatura).days
        # Adicionar 1 mês base para incluir o mês da primeira fatura
        periodo_cliente_meses = round(periodo_cliente_dias / 30.44, 1) + 1
        
        # Valor mínimo e máximo
        valor_min = faturas_dest['Soma dos itens'].min()
        valor_max = faturas_dest['Soma dos itens'].max()
        
        # Tipo de destinatário (pegar o mais comum)
        tipo_destinatario = faturas_dest['Tipo_Destinatario'].mode().iloc[0] if 'Tipo_Destinatario' in faturas_dest.columns else 'N/A'
        
        # Frequência de faturamento (faturas por mês)
        freq_faturamento = round(faturas_unicas / max(periodo_cliente_meses, 1), 2) if periodo_cliente_meses > 0 else 0
        
        # Adicionar à lista
        metricas_list.append({
            'Destinatario': destinatario,
            'Tipo_Destinatario': tipo_destinatario,
            'Meses_Desde_Ultima_Fatura': meses_desde_ultima,
            'Data_Ultima_Fatura': data_mais_recente,
            'Data_Primeira_Fatura': primeira_fatura,
            'Periodo_Cliente_Meses': periodo_cliente_meses,
            'Total_Faturas_Unicas': faturas_unicas,
            'Total_Registros': total_registros,
            'Valor_Total_Faturado': valor_total,
            'Valor_Medio_Por_Fatura': valor_medio,
            'Valor_Minimo': valor_min,
            'Valor_Maximo': valor_max,
            'Frequencia_Faturas_Por_Mes': freq_faturamento
        })
    
    # Criar DataFrame com as métricas
    df_metricas = pd.DataFrame(metricas_list)
    
    print(f"✅ Métricas calculadas para {len(df_metricas)} destinatários")
    
    return df_metricas

def criar_categorias_analise(df_metricas):
    """
    Cria categorias para facilitar a análise
    """
    print(f"\n🏷️  CRIANDO CATEGORIAS DE ANÁLISE")
    print("="*50)
    
    df_cat = df_metricas.copy()
    
    # 1. CATEGORIA POR RECÊNCIA (meses desde última fatura)
    def categorizar_recencia(meses):
        if meses <= 3:
            return 'Muito Recente (≤ 3 meses)'
        elif meses <= 6:
            return 'Recente (4-6 meses)'
        elif meses <= 12:
            return 'Moderado (7-12 meses)'
        elif meses <= 24:
            return 'Antigo (13-24 meses)'
        else:
            return 'Muito Antigo (> 24 meses)'
    
    df_cat['Categoria_Recencia'] = df_cat['Meses_Desde_Ultima_Fatura'].apply(categorizar_recencia)
    
    # 2. CATEGORIA POR VOLUME DE FATURAS
    def categorizar_volume(faturas):
        if faturas == 1:
            return 'Único (1 fatura)'
        elif faturas <= 5:
            return 'Baixo (2-5 faturas)'
        elif faturas <= 15:
            return 'Médio (6-15 faturas)'
        elif faturas <= 50:
            return 'Alto (16-50 faturas)'
        else:
            return 'Muito Alto (> 50 faturas)'
    
    df_cat['Categoria_Volume'] = df_cat['Total_Faturas_Unicas'].apply(categorizar_volume)
    
    # 3. CATEGORIA POR VALOR TOTAL
    def categorizar_valor_total(valor):
        if valor <= 5000:
            return 'Baixo (≤ R$ 5.000)'
        elif valor <= 25000:
            return 'Médio (R$ 5.001 - R$ 25.000)'
        elif valor <= 100000:
            return 'Alto (R$ 25.001 - R$ 100.000)'
        elif valor <= 500000:
            return 'Muito Alto (R$ 100.001 - R$ 500.000)'
        else:
            return 'Premium (> R$ 500.000)'
    
    df_cat['Categoria_Valor_Total'] = df_cat['Valor_Total_Faturado'].apply(categorizar_valor_total)
    
    # 4. CATEGORIA POR FREQUÊNCIA
    def categorizar_frequencia(freq):
        if freq >= 2:
            return 'Muito Frequente (≥ 2/mês)'
        elif freq >= 1:
            return 'Frequente (1-2/mês)'
        elif freq >= 0.5:
            return 'Moderado (0.5-1/mês)'
        elif freq >= 0.25:
            return 'Baixo (0.25-0.5/mês)'
        else:
            return 'Muito Baixo (< 0.25/mês)'
    
    df_cat['Categoria_Frequencia'] = df_cat['Frequencia_Faturas_Por_Mes'].apply(categorizar_frequencia)
    
    # 5. SCORE DE IMPORTÂNCIA (combinação de valor total e recência)
    # Normalizar valores para score (0-100)
    valor_norm = (df_cat['Valor_Total_Faturado'] - df_cat['Valor_Total_Faturado'].min()) / (df_cat['Valor_Total_Faturado'].max() - df_cat['Valor_Total_Faturado'].min()) * 50
    recencia_norm = np.where(df_cat['Meses_Desde_Ultima_Fatura'] <= 12, 
                            50 - (df_cat['Meses_Desde_Ultima_Fatura'] / 12 * 50), 
                            0)  # Penalizar muito quem não fatura há mais de 12 meses
    
    df_cat['Score_Importancia'] = (valor_norm + recencia_norm).round(1)
    
    def categorizar_importancia(score):
        if score >= 80:
            return 'Crítico (≥ 80)'
        elif score >= 60:
            return 'Alto (60-79)'
        elif score >= 40:
            return 'Médio (40-59)'
        elif score >= 20:
            return 'Baixo (20-39)'
        else:
            return 'Muito Baixo (< 20)'
    
    df_cat['Categoria_Importancia'] = df_cat['Score_Importancia'].apply(categorizar_importancia)
    
    print("✅ Categorias criadas:")
    print("   • Categoria_Recencia")
    print("   • Categoria_Volume") 
    print("   • Categoria_Valor_Total")
    print("   • Categoria_Frequencia")
    print("   • Score_Importancia")
    print("   • Categoria_Importancia")
    
    return df_cat

def gerar_estatisticas_resumo(df_metricas):
    """
    Gera estatísticas resumo das métricas
    """
    print(f"\n📈 ESTATÍSTICAS RESUMO")
    print("="*60)
    
    total_destinatarios = len(df_metricas)
    
    print(f"👥 TOTAL DE DESTINATÁRIOS: {total_destinatarios}")
    
    print(f"\n⏰ RECÊNCIA (Meses desde última fatura):")
    print(f"   • Média: {df_metricas['Meses_Desde_Ultima_Fatura'].mean():.1f} meses")
    print(f"   • Mediana: {df_metricas['Meses_Desde_Ultima_Fatura'].median():.1f} meses")
    print(f"   • Mínimo: {df_metricas['Meses_Desde_Ultima_Fatura'].min():.1f} meses")
    print(f"   • Máximo: {df_metricas['Meses_Desde_Ultima_Fatura'].max():.1f} meses")
    
    print(f"\n📊 VOLUME DE FATURAS:")
    print(f"   • Total de faturas únicas: {df_metricas['Total_Faturas_Unicas'].sum():,}")
    print(f"   • Média por destinatário: {df_metricas['Total_Faturas_Unicas'].mean():.1f}")
    print(f"   • Mediana: {df_metricas['Total_Faturas_Unicas'].median():.0f}")
    print(f"   • Máximo: {df_metricas['Total_Faturas_Unicas'].max()}")
    
    print(f"\n💰 VALORES FATURADOS:")
    valor_total_geral = df_metricas['Valor_Total_Faturado'].sum()
    print(f"   • Valor total geral: R$ {valor_total_geral:,.2f}")
    print(f"   • Valor médio por destinatário: R$ {df_metricas['Valor_Total_Faturado'].mean():,.2f}")
    print(f"   • Valor mediano: R$ {df_metricas['Valor_Total_Faturado'].median():,.2f}")
    print(f"   • Maior valor: R$ {df_metricas['Valor_Total_Faturado'].max():,.2f}")
    
    print(f"\n🎯 TOP 10 DESTINATÁRIOS POR VALOR:")
    top_10_valor = df_metricas.nlargest(10, 'Valor_Total_Faturado')[['Destinatario', 'Valor_Total_Faturado', 'Total_Faturas_Unicas']]
    for i, (_, row) in enumerate(top_10_valor.iterrows(), 1):
        print(f"   {i:2d}. {row['Destinatario'][:50]}...")
        print(f"       R$ {row['Valor_Total_Faturado']:,.2f} ({row['Total_Faturas_Unicas']} faturas)")
    
    # Distribuição por categorias (se existirem)
    if 'Categoria_Recencia' in df_metricas.columns:
        print(f"\n📊 DISTRIBUIÇÃO POR RECÊNCIA:")
        dist_recencia = df_metricas['Categoria_Recencia'].value_counts()
        for categoria, count in dist_recencia.items():
            pct = (count / total_destinatarios) * 100
            print(f"   • {categoria}: {count} ({pct:.1f}%)")

def main():
    """
    Função principal
    """
    print("🚀 INICIANDO CÁLCULO DE MÉTRICAS")
    print("="*80)
    
    # Carregar dados limpos
    df_faturas = carregar_dados_limpos()
    if df_faturas is None:
        return
    
    # Calcular métricas por destinatário
    df_metricas = calcular_metricas_por_destinatario(df_faturas)
    
    # Criar categorias de análise
    df_metricas_final = criar_categorias_analise(df_metricas)
    
    # Gerar estatísticas resumo
    gerar_estatisticas_resumo(df_metricas_final)
    
    # Salvar resultados
    print(f"\n💾 SALVANDO RESULTADOS")
    print("="*50)
    
    try:
        # Salvar métricas completas
        arquivo_metricas = 'data/metricas_destinatarios.xlsx'
        df_metricas_final.to_excel(arquivo_metricas, index=False)
        
        # Salvar resumo executivo (top destinatários)
        top_destinatarios = df_metricas_final.nlargest(50, 'Score_Importancia')
        arquivo_top = 'data/top_destinatarios.xlsx'
        top_destinatarios.to_excel(arquivo_top, index=False)
        
        print(f"✅ Arquivos salvos:")
        print(f"   📊 {arquivo_metricas} - Métricas completas ({len(df_metricas_final)} destinatários)")
        print(f"   🏆 {arquivo_top} - Top 50 destinatários por importância")
        
    except Exception as e:
        print(f"❌ Erro ao salvar: {str(e)}")
    
    print(f"\n{'='*80}")
    print("✅ CÁLCULO DE MÉTRICAS CONCLUÍDO!")
    print("="*80)
    
    return df_metricas_final

if __name__ == "__main__":
    df_metricas = main()
