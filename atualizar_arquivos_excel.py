#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para atualizar arquivos Excel com a correção do Periodo_Cliente_Meses
"""

import pandas as pd
import os
from datetime import datetime
import shutil

def fazer_backup(arquivo):
    """Faz backup do arquivo original"""
    if os.path.exists(arquivo):
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = arquivo.replace('.xlsx', f'_backup_{timestamp}.xlsx')
        shutil.copy2(arquivo, backup_name)
        print(f"✅ Backup criado: {backup_name}")
        return backup_name
    return None

def atualizar_clientes_legalone_fatura():
    """Atualiza o arquivo clientes_legalone_fatura.xlsx"""
    print("\n🔄 ATUALIZANDO clientes_legalone_fatura.xlsx")
    print("="*60)
    
    arquivo_original = 'data/clientes_legalone_fatura.xlsx'
    arquivo_dados_corretos = 'data/clientes_consolidado_completo.xlsx'
    
    # Fazer backup
    fazer_backup(arquivo_original)
    
    # Carregar dados corretos
    df_correto = pd.read_excel(arquivo_dados_corretos)
    print(f"📊 Dados corretos carregados: {len(df_correto)} registros")
    
    # Carregar arquivo original para preservar Sheet2
    xl_original = pd.ExcelFile(arquivo_original)
    
    # Ler Sheet2 (faturas mensais) - esta não precisa ser alterada
    sheet2 = pd.read_excel(arquivo_original, sheet_name='Sheet2')
    print(f"📋 Sheet2 preservada: {len(sheet2)} registros")
    
    # Preparar Sheet1 com dados corretos
    # Usar apenas as colunas que existem no arquivo correto
    colunas_disponiveis = [
        'Nome', 'CPF_CNPJ', 'Tipo_Destinatario', 'Meses_Desde_Ultima_Fatura',
        'Data_Ultima_Fatura', 'Data_Primeira_Fatura', 'Periodo_Cliente_Meses',
        'Total_Faturas_Unicas', 'Valor_Total_Faturado', 'Valor_Medio_Por_Fatura',
        'Valor_Minimo', 'Valor_Maximo', 'Frequencia_Faturas_Por_Mes',
        'Telefones', 'Emails', 'UF', 'CNAE', 'Regime_Tributario'
    ]

    sheet1_atualizada = df_correto[colunas_disponiveis].copy()

    # Adicionar colunas que estão no original mas não no correto (com valores vazios)
    sheet1_atualizada['Primeiro_CNAE'] = sheet1_atualizada['CNAE']  # Usar CNAE como Primeiro_CNAE
    sheet1_atualizada['Descricao_CNAE'] = ''  # Deixar vazio por enquanto
    
    # Verificar correção
    clientes_zero = len(sheet1_atualizada[sheet1_atualizada['Periodo_Cliente_Meses'] <= 0])
    min_periodo = sheet1_atualizada['Periodo_Cliente_Meses'].min()
    
    print(f"📈 Verificação da Sheet1 atualizada:")
    print(f"   • Clientes com ≤0 meses: {clientes_zero}")
    print(f"   • Mínimo período: {min_periodo:.1f} meses")
    
    # Salvar arquivo atualizado
    try:
        with pd.ExcelWriter(arquivo_original, engine='openpyxl') as writer:
            sheet1_atualizada.to_excel(writer, sheet_name='Sheet1', index=False)
            sheet2.to_excel(writer, sheet_name='Sheet2', index=False)
        print(f"✅ Arquivo atualizado: {arquivo_original}")
        return True
    except PermissionError:
        # Se não conseguir sobrescrever, criar um novo arquivo
        novo_arquivo = arquivo_original.replace('.xlsx', '_atualizado.xlsx')
        print(f"⚠️  Arquivo original está em uso. Criando novo arquivo: {novo_arquivo}")

        with pd.ExcelWriter(novo_arquivo, engine='openpyxl') as writer:
            sheet1_atualizada.to_excel(writer, sheet_name='Sheet1', index=False)
            sheet2.to_excel(writer, sheet_name='Sheet2', index=False)

        print(f"✅ Novo arquivo criado: {novo_arquivo}")
        print(f"📝 AÇÃO NECESSÁRIA: Feche o Excel e substitua manualmente o arquivo original")
        return True

def atualizar_backup_antes_faturas():
    """Atualiza o arquivo clientes_consolidado_completo_backup_antes_faturas.xlsx"""
    print("\n🔄 ATUALIZANDO clientes_consolidado_completo_backup_antes_faturas.xlsx")
    print("="*70)
    
    arquivo_backup = 'data/clientes_consolidado_completo_backup_antes_faturas.xlsx'
    arquivo_dados_corretos = 'data/clientes_consolidado_completo.xlsx'
    
    # Fazer backup do backup (backup do backup!)
    fazer_backup(arquivo_backup)
    
    # Carregar dados corretos
    df_correto = pd.read_excel(arquivo_dados_corretos)
    print(f"📊 Dados corretos carregados: {len(df_correto)} registros")
    
    # Verificar correção
    clientes_zero = len(df_correto[df_correto['Periodo_Cliente_Meses'] <= 0])
    min_periodo = df_correto['Periodo_Cliente_Meses'].min()
    
    print(f"📈 Verificação dos dados corretos:")
    print(f"   • Clientes com ≤0 meses: {clientes_zero}")
    print(f"   • Mínimo período: {min_periodo:.1f} meses")
    
    # Substituir o arquivo backup com os dados corretos
    df_correto.to_excel(arquivo_backup, index=False)
    
    print(f"✅ Arquivo backup atualizado: {arquivo_backup}")
    return True

def verificar_atualizacoes():
    """Verifica se as atualizações foram aplicadas corretamente"""
    print("\n🔍 VERIFICANDO ATUALIZAÇÕES")
    print("="*50)
    
    arquivos_para_verificar = [
        ('data/clientes_legalone_fatura.xlsx', 'Sheet1'),
        ('data/clientes_consolidado_completo_backup_antes_faturas.xlsx', 'Sheet1')
    ]
    
    todos_corretos = True
    
    for arquivo, sheet in arquivos_para_verificar:
        if os.path.exists(arquivo):
            try:
                df = pd.read_excel(arquivo, sheet_name=sheet)
                if 'Periodo_Cliente_Meses' in df.columns:
                    clientes_zero = len(df[df['Periodo_Cliente_Meses'] <= 0])
                    min_periodo = df['Periodo_Cliente_Meses'].min()
                    
                    print(f"📁 {arquivo} ({sheet}):")
                    print(f"   • Clientes com ≤0 meses: {clientes_zero}")
                    print(f"   • Mínimo período: {min_periodo:.1f} meses")
                    
                    if clientes_zero == 0 and min_periodo >= 1.0:
                        print(f"   ✅ CORRETO")
                    else:
                        print(f"   ❌ AINDA TEM PROBLEMA")
                        todos_corretos = False
                else:
                    print(f"📁 {arquivo} ({sheet}): não tem Periodo_Cliente_Meses")
            except Exception as e:
                print(f"📁 {arquivo}: ❌ Erro ao verificar: {e}")
                todos_corretos = False
        else:
            print(f"📁 {arquivo}: ❌ Arquivo não encontrado")
            todos_corretos = False
    
    return todos_corretos

def main():
    """Função principal"""
    print("🚀 ATUALIZANDO ARQUIVOS EXCEL COM CORREÇÃO")
    print("="*80)
    
    try:
        # Atualizar clientes_legalone_fatura.xlsx
        sucesso1 = atualizar_clientes_legalone_fatura()
        
        # Atualizar backup_antes_faturas.xlsx
        sucesso2 = atualizar_backup_antes_faturas()
        
        # Verificar se tudo foi atualizado corretamente
        if sucesso1 and sucesso2:
            print("\n🔍 VERIFICAÇÃO FINAL")
            print("="*50)
            
            if verificar_atualizacoes():
                print("\n🎉 TODOS OS ARQUIVOS FORAM ATUALIZADOS COM SUCESSO!")
                print("✅ Não há mais clientes com 0 meses de período")
                print("✅ Todos os clientes têm pelo menos 1 mês de período")
            else:
                print("\n❌ ALGUNS ARQUIVOS AINDA TÊM PROBLEMAS")
                print("   Verifique os logs acima para detalhes")
        else:
            print("\n❌ ERRO DURANTE A ATUALIZAÇÃO")
            
    except Exception as e:
        print(f"\n❌ ERRO GERAL: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
