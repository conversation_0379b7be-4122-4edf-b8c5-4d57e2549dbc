#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para limpeza e preparação dos dados dos arquivos Excel
- Conversão de datas
- Tratamento de valores nulos
- Identificação de PF vs PJ
- Normalização de nomes

Data: 2025-01-07
"""

import pandas as pd
import numpy as np
import re
from datetime import datetime
from pathlib import Path
import unicodedata

def carregar_dados():
    """
    Carrega os três arquivos Excel
    """
    pasta_data = Path('data')
    
    try:
        df_faturas = pd.read_excel(pasta_data / 'faturas_emitidas.xlsx')
        df_pf = pd.read_excel(pasta_data / 'base_pf_legalone.xlsx')
        df_pj = pd.read_excel(pasta_data / 'base_pj_legalone.xlsx')
        
        print("Dados carregados com sucesso!")
        print(f" Faturas: {len(df_faturas)} registros")
        print(f" PF: {len(df_pf)} registros")
        print(f" PJ: {len(df_pj)} registros")
        
        return df_faturas, df_pf, df_pj
        
    except Exception as e:
        print(f"Erro ao carregar dados: {str(e)}")
        return None, None, None

def normalizar_texto(texto):
    """
    Normaliza texto removendo acentos, espaços extras e padronizando maiúsculas
    """
    if pd.isna(texto) or texto == '':
        return None
    
    # Converter para string se não for
    texto = str(texto)
    
    # Remover acentos
    texto = unicodedata.normalize('NFD', texto)
    texto = ''.join(char for char in texto if unicodedata.category(char) != 'Mn')
    
    # Padronizar espaços e maiúsculas
    texto = re.sub(r'\s+', ' ', texto.strip().upper())
    
    return texto

def limpar_faturas(df_faturas):
    """
    Limpa e prepara os dados das faturas
    """
    print("\nLIMPANDO DADOS DAS FATURAS")
    print("="*50)
    
    df_clean = df_faturas.copy()
    
    # 1. Converter coluna 'Emitida em' para datetime
    print("Convertendo datas...")
    
    def converter_data(data_str):
        """Converte string de data para datetime"""
        if pd.isna(data_str):
            return None
        
        try:
            # Tentar diferentes formatos de data
            formatos = ['%d/%m/%Y', '%Y-%m-%d', '%d-%m-%Y', '%m/%d/%Y']
            
            for formato in formatos:
                try:
                    return pd.to_datetime(data_str, format=formato)
                except:
                    continue
            
            # Se nenhum formato funcionou, tentar conversão automática
            return pd.to_datetime(data_str, dayfirst=True)
            
        except:
            print(f" Não foi possível converter data: {data_str}")
            return None
    
    df_clean['Emitida em'] = df_clean['Emitida em'].apply(converter_data)
    
    # Verificar conversão
    datas_nulas = df_clean['Emitida em'].isna().sum()
    print(f"Datas convertidas. {datas_nulas} valores nulos restantes")
    
    # 2. Limpar e normalizar destinatários
    print("Normalizando destinatários...")
    df_clean['Destinatario_Normalizado'] = df_clean['Destinatário da fatura'].apply(normalizar_texto)
    
    # 3. Tratar valores nulos
    print("Tratando valores nulos...")
    
    # Preencher Status nulo com 'Não Informado'
    df_clean['Status'] = df_clean['Status'].fillna('Não Informado')
    
    # Preencher Tipo nulo com 'Não Informado'
    df_clean['Tipo'] = df_clean['Tipo'].fillna('Não Informado')
    
    # Valores nulos em Soma dos itens = 0
    df_clean['Soma dos itens'] = df_clean['Soma dos itens'].fillna(0)
    
    # 4. Criar colunas auxiliares
    print("Criando colunas auxiliares...")
    
    # Extrair ano e mês da emissão
    df_clean['Ano_Emissao'] = df_clean['Emitida em'].dt.year
    df_clean['Mes_Emissao'] = df_clean['Emitida em'].dt.month
    df_clean['Ano_Mes'] = df_clean['Emitida em'].dt.to_period('M')
    
    # Categorizar valores das faturas
    def categorizar_valor(valor):
        if pd.isna(valor) or valor == 0:
            return 'Sem Valor'
        elif valor <= 1000:
            return 'Baixo (≤ R$ 1.000)'
        elif valor <= 5000:
            return 'Médio (R$ 1.001 - R$ 5.000)'
        elif valor <= 20000:
            return 'Alto (R$ 5.001 - R$ 20.000)'
        else:
            return 'Muito Alto (> R$ 20.000)'
    
    df_clean['Categoria_Valor'] = df_clean['Soma dos itens'].apply(categorizar_valor)
    
    print(f"Faturas limpas: {len(df_clean)} registros")
    
    return df_clean

def limpar_pf(df_pf):
    """
    Limpa e prepara os dados de pessoas físicas
    """
    print("\nLIMPANDO DADOS PESSOAS FÍSICAS")
    print("="*50)
    
    df_clean = df_pf.copy()
    
    # Normalizar nomes
    print("Normalizando nomes...")
    df_clean['Nome_Normalizado'] = df_clean['Nome'].apply(normalizar_texto)
    
    # Limpar CPF (manter apenas números)
    print("Limpando CPF...")
    def limpar_cpf(cpf):
        if pd.isna(cpf):
            return None
        cpf_limpo = re.sub(r'[^\d]', '', str(cpf))
        return cpf_limpo if len(cpf_limpo) == 11 else None
    
    df_clean['CPF_Limpo'] = df_clean['CPF'].apply(limpar_cpf)
    
    # Padronizar sexo
    print("Padronizando sexo...")
    def padronizar_sexo(sexo):
        if pd.isna(sexo):
            return 'Não Informado'
        sexo = str(sexo).upper()
        if sexo in ['M', 'MASCULINO', 'MASC']:
            return 'Masculino'
        elif sexo in ['F', 'FEMININO', 'FEM']:
            return 'Feminino'
        else:
            return 'Não Informado'
    
    df_clean['Sexo_Padronizado'] = df_clean['Sexo'].apply(padronizar_sexo)
    
    print(f"PF limpa: {len(df_clean)} registros")
    
    return df_clean

def limpar_pj(df_pj):
    """
    Limpa e prepara os dados de pessoas jurídicas
    """
    print("\nLIMPANDO DADOS PESSOAS JURÍDICAS")
    print("="*50)
    
    df_clean = df_pj.copy()
    
    # Normalizar razão social
    print("Normalizando razão social...")
    df_clean['Razao_Social_Normalizada'] = df_clean['Razão social'].apply(normalizar_texto)
    
    # Limpar CNPJ (manter apenas números)
    print("Limpando CNPJ...")
    def limpar_cnpj(cnpj):
        if pd.isna(cnpj):
            return None
        cnpj_limpo = re.sub(r'[^\d]', '', str(cnpj))
        return cnpj_limpo if len(cnpj_limpo) == 14 else None
    
    df_clean['CNPJ_Limpo'] = df_clean['CNPJ'].apply(limpar_cnpj)
    
    # Padronizar regime tributário
    print("Padronizando regime tributário...")
    def padronizar_regime(regime):
        if pd.isna(regime) or str(regime).strip() in ['', '000', '00000', '---']:
            return 'Não Informado'
        return str(regime).strip()
    
    df_clean['Regime_Tributario_Padronizado'] = df_clean['Regime Tributário'].apply(padronizar_regime)
    
    print(f"PJ limpa: {len(df_clean)} registros")
    
    return df_clean

def identificar_tipo_destinatario(destinatario, df_pf_clean, df_pj_clean):
    """
    Identifica se um destinatário é Pessoa Física ou Jurídica
    """
    if pd.isna(destinatario):
        return 'Não Identificado', None, 0
    
    destinatario_norm = normalizar_texto(destinatario)
    
    # Buscar em PF
    match_pf = df_pf_clean[df_pf_clean['Nome_Normalizado'] == destinatario_norm]
    
    # Buscar em PJ
    match_pj = df_pj_clean[df_pj_clean['Razao_Social_Normalizada'] == destinatario_norm]
    
    # Determinar tipo baseado nas correspondências
    if len(match_pf) > 0 and len(match_pj) == 0:
        return 'PF', match_pf.iloc[0]['Nome'], 1.0
    elif len(match_pj) > 0 and len(match_pf) == 0:
        return 'PJ', match_pj.iloc[0]['Razão social'], 1.0
    elif len(match_pf) > 0 and len(match_pj) > 0:
        return 'Ambos', f"PF: {match_pf.iloc[0]['Nome']} | PJ: {match_pj.iloc[0]['Razão social']}", 0.5
    else:
        # Tentar correspondência parcial (fuzzy matching básico)
        return buscar_correspondencia_parcial(destinatario_norm, df_pf_clean, df_pj_clean)

def buscar_correspondencia_parcial(destinatario_norm, df_pf_clean, df_pj_clean):
    """
    Busca correspondência parcial usando similaridade de strings
    """
    # Buscar correspondências parciais em PF
    pf_matches = []
    for nome in df_pf_clean['Nome_Normalizado'].dropna():
        if nome in destinatario_norm or destinatario_norm in nome:
            pf_matches.append(nome)
    
    # Buscar correspondências parciais em PJ
    pj_matches = []
    for razao in df_pj_clean['Razao_Social_Normalizada'].dropna():
        if razao in destinatario_norm or destinatario_norm in razao:
            pj_matches.append(razao)
    
    if pf_matches and not pj_matches:
        return 'PF (Parcial)', pf_matches[0], 0.7
    elif pj_matches and not pf_matches:
        return 'PJ (Parcial)', pj_matches[0], 0.7
    elif pf_matches and pj_matches:
        return 'Ambos (Parcial)', f"PF: {pf_matches[0]} | PJ: {pj_matches[0]}", 0.3
    else:
        return 'Não Encontrado', None, 0

def classificar_destinatarios(df_faturas_clean, df_pf_clean, df_pj_clean):
    """
    Classifica todos os destinatários das faturas
    """
    print("\nCLASSIFICANDO DESTINATÁRIOS")
    print("="*50)
    
    # Aplicar classificação
    print("Identificando tipos de destinatários...")
    
    resultados = df_faturas_clean['Destinatario_Normalizado'].apply(
        lambda x: identificar_tipo_destinatario(x, df_pf_clean, df_pj_clean)
    )
    
    # Separar os resultados
    df_faturas_clean['Tipo_Destinatario'] = [r[0] for r in resultados]
    df_faturas_clean['Nome_Encontrado'] = [r[1] for r in resultados]
    df_faturas_clean['Confianca_Match'] = [r[2] for r in resultados]
    
    # Estatísticas da classificação
    print("\nESTATÍSTICAS DA CLASSIFICAÇÃO:")
    print("-" * 40)
    stats = df_faturas_clean['Tipo_Destinatario'].value_counts()
    for tipo, count in stats.items():
        pct = (count / len(df_faturas_clean)) * 100
        print(f"   {tipo}: {count} ({pct:.1f}%)")
    
    return df_faturas_clean

def main():
    """
    Função principal
    """
    print("INICIANDO LIMPEZA E PREPARAÇÃO DOS DADOS")
    print("="*80)
    
    # Carregar dados
    df_faturas, df_pf, df_pj = carregar_dados()
    if df_faturas is None:
        return
    
    # Limpar cada dataset
    df_faturas_clean = limpar_faturas(df_faturas)
    df_pf_clean = limpar_pf(df_pf)
    df_pj_clean = limpar_pj(df_pj)
    
    # Classificar destinatários
    df_faturas_final = classificar_destinatarios(df_faturas_clean, df_pf_clean, df_pj_clean)
    
    # Salvar dados limpos
    print(f"\nSALVANDO DADOS LIMPOS")
    print("="*50)
    
    try:
        df_faturas_final.to_excel('data/faturas_limpa.xlsx', index=False)
        df_pf_clean.to_excel('data/pf_limpa.xlsx', index=False)
        df_pj_clean.to_excel('data/pj_limpa.xlsx', index=False)
        
        print("Dados salvos com sucesso!")
        print("faturas_limpa.xlsx")
        print("pf_limpa.xlsx")
        print("pj_limpa.xlsx")
        
    except Exception as e:
        print(f"Erro ao salvar: {str(e)}")
    
    print(f"\n{'='*80}")
    print("LIMPEZA CONCLUÍDA COM SUCESSO!")
    print("="*80)
    
    return df_faturas_final, df_pf_clean, df_pj_clean

if __name__ == "__main__":
    df_faturas_final, df_pf_clean, df_pj_clean = main()
