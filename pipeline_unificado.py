#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Pipeline Unificado para Processamento de Dados LegalOne
- Carrega dados do arquivo dados_legalone_novo.xlsx
- Aplica mapeamento de colunas conforme especificado
- Realiza limpeza e normalização dos dados
- Calcula métricas por cliente
- Consolida dados completos
- Gera arquivo final para análise

Autor: Análise de Dados LegalOne
Data: 2025-01-23
"""

import pandas as pd
import re
from datetime import datetime
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Mapeamento de colunas conforme especificado pelo usuário
MAPEAMENTO_COLUNAS = {
    'ID': 'id',
    'Vencimento': 'vencimento',
    'Classificações / Sub-Classificação / Mês/ano competência': 'competencia',
    'Movimentos / Recebido em': 'recebido_em',
    'Classificações / Plano de contas': 'plano_de_contas',
    'Descrição': 'descricao',
    'Movimentos / Conta corrente': 'conta_corrente',
    'Valor líquido realizado': 'valor_realizado',
    'Valor líquido a realizar': 'valor_a_realizar',
    'Classificações / Destinatário da fatura / Nome/razão social': 'nome',
    'Credor/Devedor / CPF/CNPJ': 'cpf_cnpj',
    'Proprietário / Nome/razão social': 'proprietario',
    'Usuário que cadastrou': 'usuario_que_cadastrou',
    'N° da parcela': 'num_parcela',
    'N° total de parcelas': 'num_total_parcelas',
    'Classificações / Área': 'area',
    'Movimentos / Realizado em': 'realizado_em',
    'COFINS': 'cofins',
    'CSLL': 'csll',
    'INSS': 'inss',
    'ISS': 'iss',
    'Juros': 'juros',
    'Multa': 'multa',
    'PIS': 'pis',
    'Desconto': 'desconto',
    'Classificações / Valor a classificar': 'valor_a_classificar',
    'Recebimento (Real)': 'data_recebimento',
    'IRRF': 'irrf',
    'Classificações  / ... /  E-mail': 'email',
    'Credor/Devedor / Telefones / Número': 'telefone',
    'Credor/Devedor / Endereços / CEP': 'cep'
}

def carregar_dados_novo():
    """
    Carrega o arquivo dados_legalone_novo.xlsx e aplica o mapeamento de colunas
    """
    print("🔄 CARREGANDO DADOS DO ARQUIVO NOVO")
    print("="*60)
    
    pasta_data = Path('data')
    arquivo = pasta_data / 'dados_legalone_novo.xlsx'
    
    try:
        # Carregar dados
        df = pd.read_excel(arquivo)
        print(f"✅ Dados carregados: {len(df)} registros, {len(df.columns)} colunas")
        
        # Aplicar mapeamento de colunas
        print("🔄 Aplicando mapeamento de colunas...")
        
        # Verificar quais colunas existem no arquivo
        colunas_existentes = []
        colunas_mapeadas = {}
        
        for col_original, col_nova in MAPEAMENTO_COLUNAS.items():
            if col_original in df.columns:
                colunas_existentes.append(col_original)
                colunas_mapeadas[col_original] = col_nova
            else:
                print(f"⚠️  Coluna não encontrada: {col_original}")
        
        # Selecionar apenas as colunas que existem e renomear
        df_mapeado = df[colunas_existentes].copy()
        df_mapeado = df_mapeado.rename(columns=colunas_mapeadas)
        
        print(f"✅ Mapeamento aplicado: {len(df_mapeado.columns)} colunas mapeadas")
        print(f"   Colunas finais: {list(df_mapeado.columns)}")
        
        return df_mapeado
        
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {str(e)}")
        return None



def converter_data(data_str):
    """
    Converte string de data para datetime
    """
    if pd.isna(data_str):
        return None
    
    try:
        # Tentar diferentes formatos de data
        formatos = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y', '%m/%d/%Y']
        
        for formato in formatos:
            try:
                return pd.to_datetime(data_str, format=formato)
            except:
                continue
        
        # Se nenhum formato funcionou, tentar conversão automática
        return pd.to_datetime(data_str, dayfirst=True)
        
    except:
        print(f"⚠️  Não foi possível converter data: {data_str}")
        return None

def limpar_cpf_cnpj_apenas_numeros(documento):
    """
    Remove apenas pontos, barras e hífens do CPF/CNPJ, mantendo como string
    """
    if pd.isna(documento):
        return None

    # Remover apenas formatação, manter como string
    doc_limpo = str(documento).replace('.', '').replace('/', '').replace('-', '').replace(' ', '')

    # Retornar apenas se tiver números, garantindo que seja string
    if doc_limpo.isdigit():
        return str(doc_limpo)  # Garantir que seja string
    else:
        return None

def preparar_dados(df):
    """
    Prepara os dados mantendo formato original conforme solicitado
    """
    print("\n🔄 PREPARANDO DADOS")
    print("="*60)

    df_prep = df.copy()

    # Limpar CPF/CNPJ (apenas remover formatação, manter como string)
    if 'cpf_cnpj' in df_prep.columns:
        print("🔄 Limpando formatação de CPF/CNPJ (mantendo como string)...")
        df_prep['cpf_cnpj_limpo'] = df_prep['cpf_cnpj'].apply(limpar_cpf_cnpj_apenas_numeros)

    # Apenas converter datas quando necessário para cálculos
    colunas_data = ['vencimento', 'recebido_em', 'realizado_em', 'data_recebimento']
    for col in colunas_data:
        if col in df_prep.columns:
            print(f"🔄 Preparando datas da coluna {col} para cálculos...")
            df_prep[f'{col}_para_calculo'] = df_prep[col].apply(converter_data)

    # Para competência, manter original e criar versão para cálculo se necessário
    if 'competencia' in df_prep.columns:
        print("🔄 Preparando competência (mantendo formato original)...")
        # Manter original intacto, criar versão para cálculo apenas se necessário
        def converter_competencia_para_data(comp_str):
            if pd.isna(comp_str):
                return None
            try:
                # Se for formato MM/AAAA, converter para primeiro dia do mês
                comp_str = str(comp_str).strip()
                if '/' in comp_str and len(comp_str) == 7:  # MM/AAAA
                    mes, ano = comp_str.split('/')
                    return pd.to_datetime(f'{ano}-{mes.zfill(2)}-01')
                else:
                    return converter_data(comp_str)
            except:
                return None

        df_prep['competencia_para_calculo'] = df_prep['competencia'].apply(converter_competencia_para_data)

    print(f"✅ Dados preparados: {len(df_prep)} registros")
    print("   ⚠️  Nomes e valores mantidos no formato original")
    print("   ✅ CPF/CNPJ limpo (apenas números, mantido como string)")

    return df_prep

def calcular_metricas_por_cliente(df):
    """
    Calcula métricas por cliente mantendo nomes originais
    """
    print("\n📊 CALCULANDO MÉTRICAS POR CLIENTE")
    print("="*60)

    # Usar data de recebimento como referência principal
    data_col = 'recebido_em_para_calculo'
    if data_col not in df.columns or df[data_col].isna().all():
        data_col = 'realizado_em_para_calculo'
    if data_col not in df.columns or df[data_col].isna().all():
        data_col = 'data_recebimento_para_calculo'

    if data_col not in df.columns or df[data_col].isna().all():
        print("❌ Nenhuma coluna de data válida encontrada")
        return None

    # Filtrar registros com data válida
    df_valido = df[df[data_col].notna()].copy()
    print(f"📅 Usando coluna de data: {data_col}")
    print(f"📊 Registros com data válida: {len(df_valido)}")

    # Data de referência para cálculos
    data_referencia = datetime.now().date()

    # Agrupar por cliente usando nome original (sem normalização)
    clientes = df_valido['nome'].dropna().unique()
    print(f"👥 Clientes únicos encontrados: {len(clientes)}")

    metricas_list = []

    for i, cliente in enumerate(clientes, 1):
        if i % 50 == 0:
            print(f"   Processando cliente {i}/{len(clientes)}...")

        # Filtrar dados do cliente usando nome original
        dados_cliente = df_valido[df_valido['nome'] == cliente].copy()

        # Calcular métricas
        data_mais_recente = dados_cliente[data_col].max()
        data_primeira = dados_cliente[data_col].min()

        # Meses desde última movimentação
        dias_desde_ultima = (data_referencia - data_mais_recente.date()).days
        meses_desde_ultima = round(dias_desde_ultima / 30.44, 1)

        # Período como cliente (incluindo primeiro mês)
        periodo_dias = (data_mais_recente - data_primeira).days
        periodo_meses = round(periodo_dias / 30.44, 1) + 1

        # Valores separados (conforme solicitado)
        valor_total_realizado = dados_cliente['valor_realizado'].sum() if 'valor_realizado' in dados_cliente.columns else 0
        valor_total_a_realizar = dados_cliente['valor_a_realizar'].sum() if 'valor_a_realizar' in dados_cliente.columns else 0

        # Valor total faturado = APENAS valor realizado (conforme correção)
        valor_total_faturado = valor_total_realizado

        # Número de movimentações
        total_movimentacoes = len(dados_cliente)

        # Valor médio por movimentação (baseado apenas no valor realizado)
        valor_medio = valor_total_faturado / total_movimentacoes if total_movimentacoes > 0 else 0

        # Frequência (movimentações por mês)
        frequencia = round(total_movimentacoes / max(periodo_meses, 1), 2)

        # Dados adicionais (usando CPF/CNPJ limpo se disponível)
        cpf_cnpj_final = dados_cliente['cpf_cnpj_limpo'].iloc[0] if 'cpf_cnpj_limpo' in dados_cliente.columns else None
        if pd.isna(cpf_cnpj_final):
            cpf_cnpj_final = dados_cliente['cpf_cnpj'].iloc[0] if 'cpf_cnpj' in dados_cliente.columns else None

        # Consolidar telefones únicos (mantendo formato original)
        telefones_unicos = dados_cliente['telefone'].dropna().unique() if 'telefone' in dados_cliente.columns else []
        telefone_consolidado = '; '.join([str(t) for t in telefones_unicos if pd.notna(t)]) if len(telefones_unicos) > 0 else None

        # Consolidar emails únicos (mantendo formato original)
        emails_unicos = dados_cliente['email'].dropna().unique() if 'email' in dados_cliente.columns else []
        email_consolidado = '; '.join([str(e) for e in emails_unicos if pd.notna(e)]) if len(emails_unicos) > 0 else None

        # Consolidar CEPs únicos
        ceps_unicos = dados_cliente['cep'].dropna().unique() if 'cep' in dados_cliente.columns else []
        cep_consolidado = '; '.join([str(c) for c in ceps_unicos if pd.notna(c)]) if len(ceps_unicos) > 0 else None

        # Área mais comum
        area_comum = dados_cliente['area'].mode().iloc[0] if 'area' in dados_cliente.columns and not dados_cliente['area'].empty else None

        metricas_list.append({
            'nome_original': cliente,  # Mantendo nome exatamente como está no arquivo
            'cpf_cnpj': cpf_cnpj_final,  # CPF/CNPJ limpo (apenas números)
            'telefones': telefone_consolidado,
            'emails': email_consolidado,
            'cep': cep_consolidado,
            'area': area_comum,
            'meses_desde_ultima': meses_desde_ultima,
            'data_ultima': data_mais_recente.strftime('%d/%m/%Y') if pd.notna(data_mais_recente) else None,
            'data_primeira': data_primeira.strftime('%d/%m/%Y') if pd.notna(data_primeira) else None,
            'periodo_meses': periodo_meses,
            'total_movimentacoes': total_movimentacoes,
            'valor_total_realizado': round(valor_total_realizado, 2),
            'valor_total_a_realizar': round(valor_total_a_realizar, 2),
            'valor_total_faturado': round(valor_total_faturado, 2),  # APENAS valor realizado
            'valor_medio': round(valor_medio, 2),
            'frequencia_mensal': frequencia
        })

    df_metricas = pd.DataFrame(metricas_list)

    # Garantir que CPF/CNPJ seja mantido como string
    if 'cpf_cnpj' in df_metricas.columns:
        df_metricas['cpf_cnpj'] = df_metricas['cpf_cnpj'].astype(str)
        # Substituir 'nan' por None para valores nulos
        df_metricas['cpf_cnpj'] = df_metricas['cpf_cnpj'].replace('nan', None)

    print(f"✅ Métricas calculadas para {len(df_metricas)} clientes")

    return df_metricas

def gerar_relatorio_executivo(df_metricas):
    """
    Gera relatório executivo com estatísticas principais
    """
    print("\n📋 GERANDO RELATÓRIO EXECUTIVO")
    print("="*60)

    relatorio = []

    # Estatísticas gerais
    total_clientes = len(df_metricas)
    valor_total_geral = df_metricas['valor_total_faturado'].sum()
    valor_medio_cliente = df_metricas['valor_total_faturado'].mean()

    relatorio.append("=== RELATÓRIO EXECUTIVO - ANÁLISE DE CLIENTES LEGALONE ===")
    relatorio.append(f"Data de geração: {datetime.now().strftime('%d/%m/%Y %H:%M:%S')}")
    relatorio.append("")
    relatorio.append("📊 ESTATÍSTICAS GERAIS:")
    relatorio.append(f"   • Total de clientes únicos: {total_clientes:,}")
    relatorio.append(f"   • Valor total movimentado: R$ {valor_total_geral:,.2f}")
    relatorio.append(f"   • Valor médio por cliente: R$ {valor_medio_cliente:,.2f}")
    relatorio.append("")

    # Dados de contato
    com_email = df_metricas['emails'].notna().sum()
    com_telefone = df_metricas['telefones'].notna().sum()
    com_cep = df_metricas['cep'].notna().sum()

    relatorio.append("📞 DADOS DE CONTATO:")
    relatorio.append(f"   • Clientes com email: {com_email:,} ({com_email/total_clientes*100:.1f}%)")
    relatorio.append(f"   • Clientes com telefone: {com_telefone:,} ({com_telefone/total_clientes*100:.1f}%)")
    relatorio.append(f"   • Clientes com CEP: {com_cep:,} ({com_cep/total_clientes*100:.1f}%)")
    relatorio.append("")

    # Análise temporal
    clientes_ativos = (df_metricas['meses_desde_ultima'] <= 12).sum()
    clientes_inativos = (df_metricas['meses_desde_ultima'] > 12).sum()

    relatorio.append("⏰ ANÁLISE TEMPORAL:")
    relatorio.append(f"   • Clientes ativos (≤ 12 meses): {clientes_ativos:,} ({clientes_ativos/total_clientes*100:.1f}%)")
    relatorio.append(f"   • Clientes inativos (> 12 meses): {clientes_inativos:,} ({clientes_inativos/total_clientes*100:.1f}%)")
    relatorio.append("")

    # Top 10 clientes
    top10 = df_metricas.nlargest(10, 'valor_total_faturado')
    relatorio.append("🏆 TOP 10 CLIENTES POR VALOR TOTAL FATURADO:")
    for i, (_, cliente) in enumerate(top10.iterrows(), 1):
        relatorio.append(f"   {i:2d}. {cliente['nome_original'][:50]:<50} R$ {cliente['valor_total_faturado']:>12,.2f}")

    # Salvar relatório
    relatorio_texto = '\n'.join(relatorio)

    with open('data/relatorio_executivo.txt', 'w', encoding='utf-8') as f:
        f.write(relatorio_texto)

    print("✅ Relatório executivo salvo: relatorio_executivo.txt")

    return relatorio_texto

if __name__ == "__main__":
    print("🚀 INICIANDO PIPELINE UNIFICADO LEGALONE")
    print("="*80)

    # 1. Carregar dados
    df_dados = carregar_dados_novo()
    if df_dados is None:
        exit(1)

    # 2. Preparar dados (sem normalização)
    df_preparado = preparar_dados(df_dados)

    # 3. Calcular métricas
    df_metricas = calcular_metricas_por_cliente(df_preparado)

    if df_metricas is not None:
        # 4. Gerar relatório executivo
        relatorio = gerar_relatorio_executivo(df_metricas)

        # 5. Salvar resultados
        print(f"\n💾 SALVANDO RESULTADOS")
        print("="*50)

        try:
            # Salvar dados preparados (mantendo formato original)
            df_preparado.to_excel('data/dados_preparados_novo.xlsx', index=False)
            print("✅ Dados preparados salvos: dados_preparados_novo.xlsx")

            # Salvar métricas ordenadas por valor total faturado (apenas valor realizado)
            df_metricas_final = df_metricas.sort_values('valor_total_faturado', ascending=False)

            # Preparar CPF/CNPJ com prefixo para forçar formato texto no Excel
            df_excel = df_metricas_final.copy()
            if 'cpf_cnpj' in df_excel.columns:
                # Adicionar prefixo ' (aspas simples) para forçar formato texto
                df_excel['cpf_cnpj'] = df_excel['cpf_cnpj'].apply(
                    lambda x: f"'{x}" if pd.notna(x) and str(x) != 'None' else x
                )

            # Salvar Excel
            df_excel.to_excel('data/metricas_clientes_novo.xlsx', index=False)
            print("✅ Métricas salvos: metricas_clientes_novo.xlsx")

            # Salvar também versão CSV (mantém formato original)
            df_metricas_final.to_csv('data/metricas_clientes_novo.csv', index=False, encoding='utf-8-sig')
            print("✅ Métricas salvos também em CSV: metricas_clientes_novo.csv")

            # Salvar também versão filtrada para clientes ativos (últimos 12 meses)
            df_ativos = df_metricas_final[df_metricas_final['meses_desde_ultima'] <= 12]

            # Preparar versão Excel dos ativos
            df_ativos_excel = df_ativos.copy()
            if 'cpf_cnpj' in df_ativos_excel.columns:
                df_ativos_excel['cpf_cnpj'] = df_ativos_excel['cpf_cnpj'].apply(
                    lambda x: f"'{x}" if pd.notna(x) and str(x) != 'None' else x
                )

            df_ativos_excel.to_excel('data/clientes_ativos_12meses.xlsx', index=False)
            print("✅ Clientes ativos salvos: clientes_ativos_12meses.xlsx")

            # Salvar CSV dos ativos também
            df_ativos.to_csv('data/clientes_ativos_12meses.csv', index=False, encoding='utf-8-sig')
            print("✅ Clientes ativos salvos também em CSV: clientes_ativos_12meses.csv")

            print(f"\n📊 RESUMO FINAL:")
            print(f"   📋 {len(df_preparado)} registros processados")
            print(f"   👥 {len(df_metricas)} clientes únicos")
            print(f"   🟢 {len(df_ativos)} clientes ativos (≤ 12 meses)")
            print(f"   💰 Valor total faturado (realizado): R$ {df_metricas['valor_total_faturado'].sum():,.2f}")
            print(f"   💸 Valor total a realizar: R$ {df_metricas['valor_total_a_realizar'].sum():,.2f}")
            print(f"   📞 {df_metricas['emails'].notna().sum()} clientes com email")
            print(f"   📱 {df_metricas['telefones'].notna().sum()} clientes com telefone")

        except Exception as e:
            print(f"❌ Erro ao salvar: {str(e)}")

    print(f"\n{'='*80}")
    print("✅ PIPELINE CONCLUÍDO!")
    print("Arquivos gerados:")
    print("  • dados_preparados_novo.xlsx - Dados com formato original mantido")
    print("  • metricas_clientes_novo.xlsx - Métricas por cliente (Excel)")
    print("  • metricas_clientes_novo.csv - Métricas por cliente (CSV)")
    print("  • clientes_ativos_12meses.xlsx - Clientes ativos (Excel)")
    print("  • clientes_ativos_12meses.csv - Clientes ativos (CSV)")
    print("  • relatorio_executivo.txt - Relatório resumo")
    print("")
    print("📝 NOTA: CPF/CNPJ limpo (apenas números):")
    print("   - Arquivos Excel: CPF/CNPJ com prefixo ' para forçar formato texto")
    print("   - Arquivos CSV: CPF/CNPJ sem formatação adicional")
    print("="*80)
