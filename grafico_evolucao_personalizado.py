#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script personalizado para gerar gráfico da evolução mensal dos top N clientes
Permite personalizar número de clientes, período e formato do gráfico
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np
import argparse

def carregar_dados():
    """Carrega os dados das duas sheets do arquivo Excel"""
    file_path = 'data/clientes_legalone_fatura.xlsx'
    
    # Carregar dados dos clientes (Sheet1)
    clientes_df = pd.read_excel(file_path, sheet_name='Sheet1')
    
    # Carregar dados das faturas mensais (Sheet2)
    faturas_df = pd.read_excel(file_path, sheet_name='Sheet2')
    
    return clientes_df, faturas_df

def identificar_top_clientes(clientes_df, n_clientes=5):
    """Identifica os top N clientes com maior valor total de faturamento"""
    # Ordenar por valor total faturado (decrescente) e pegar os top N
    top_clientes = clientes_df.nlargest(n_clientes, 'Valor_Total_Faturado')
    
    print(f"=== TOP {n_clientes} CLIENTES POR VALOR TOTAL FATURADO ===")
    for i, (idx, cliente) in enumerate(top_clientes.iterrows(), 1):
        print(f"{i}. {cliente['Nome']}")
        print(f"   Valor Total: R$ {cliente['Valor_Total_Faturado']:,.2f}")
        print(f"   CPF/CNPJ: {cliente['CPF_CNPJ']}")
        print()
    
    return top_clientes

def preparar_dados_evolucao(top_clientes, faturas_df, data_inicio=None, data_fim=None):
    """Prepara os dados de evolução mensal para os top clientes"""
    # Converter coluna de data para datetime (formato brasileiro dd/mm/yyyy)
    faturas_df['Data_Fatura'] = pd.to_datetime(faturas_df['Data_Fatura'], format='%d/%m/%Y', errors='coerce')
    
    # Filtrar por período se especificado
    if data_inicio:
        faturas_df = faturas_df[faturas_df['Data_Fatura'] >= data_inicio]
    if data_fim:
        faturas_df = faturas_df[faturas_df['Data_Fatura'] <= data_fim]
    
    # Criar coluna de ano-mês para agrupamento
    faturas_df['Ano_Mes'] = faturas_df['Data_Fatura'].dt.to_period('M')
    
    # Filtrar faturas apenas dos top clientes
    top_nomes = top_clientes['Nome'].tolist()
    faturas_top = faturas_df[faturas_df['Nome'].isin(top_nomes)].copy()
    
    # Agrupar por cliente e mês, somando os valores
    evolucao_mensal = faturas_top.groupby(['Nome', 'Ano_Mes'])['Valor_Fatura'].sum().reset_index()
    
    # Converter período de volta para datetime para facilitar o plot
    evolucao_mensal['Data'] = evolucao_mensal['Ano_Mes'].dt.to_timestamp()
    
    return evolucao_mensal

def criar_grafico_personalizado(evolucao_mensal, top_clientes, titulo_personalizado=None, 
                               mostrar_valores=False, estilo_grafico='linha'):
    """Cria o gráfico de evolução mensal personalizado"""
    # Configurar o estilo
    plt.style.use('seaborn-v0_8')
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # Definir paleta de cores
    cores = plt.cm.Set3(np.linspace(0, 1, len(top_clientes)))
    
    # Plotar para cada cliente
    for i, (idx, cliente) in enumerate(top_clientes.iterrows()):
        nome_cliente = cliente['Nome']
        dados_cliente = evolucao_mensal[evolucao_mensal['Nome'] == nome_cliente]
        
        if not dados_cliente.empty:
            # Ordenar por data
            dados_cliente = dados_cliente.sort_values('Data')
            
            if estilo_grafico == 'linha':
                ax.plot(dados_cliente['Data'], 
                       dados_cliente['Valor_Fatura'], 
                       marker='o', 
                       linewidth=3,
                       markersize=8,
                       color=cores[i],
                       label=f"{nome_cliente[:40]}..." if len(nome_cliente) > 40 else nome_cliente)
            
            elif estilo_grafico == 'area':
                ax.fill_between(dados_cliente['Data'], 
                               dados_cliente['Valor_Fatura'],
                               alpha=0.7,
                               color=cores[i],
                               label=f"{nome_cliente[:40]}..." if len(nome_cliente) > 40 else nome_cliente)
            
            # Adicionar valores nos pontos se solicitado
            if mostrar_valores:
                for x, y in zip(dados_cliente['Data'], dados_cliente['Valor_Fatura']):
                    ax.annotate(f'R$ {y:,.0f}', (x, y), 
                               textcoords="offset points", 
                               xytext=(0,10), 
                               ha='center', 
                               fontsize=8,
                               alpha=0.8)
    
    # Configurar o gráfico
    titulo = titulo_personalizado or f'Evolução Mensal do Faturamento - Top {len(top_clientes)} Clientes'
    ax.set_title(titulo, fontsize=18, fontweight='bold', pad=25)
    ax.set_xlabel('Período', fontsize=14, fontweight='bold')
    ax.set_ylabel('Valor Faturado (R$)', fontsize=14, fontweight='bold')
    
    # Formatar eixo Y para mostrar valores em reais
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))
    
    # Rotacionar labels do eixo X
    plt.xticks(rotation=45)
    
    # Configurar legenda
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=11)
    
    # Adicionar grid
    ax.grid(True, alpha=0.4, linestyle='--')
    
    # Melhorar aparência
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    
    # Ajustar layout
    plt.tight_layout()
    
    return fig

def main():
    """Função principal com argumentos personalizáveis"""
    parser = argparse.ArgumentParser(description='Gerar gráfico de evolução dos top clientes')
    parser.add_argument('--clientes', '-n', type=int, default=5, 
                       help='Número de top clientes a exibir (padrão: 5)')
    parser.add_argument('--titulo', '-t', type=str, 
                       help='Título personalizado para o gráfico')
    parser.add_argument('--valores', '-v', action='store_true', 
                       help='Mostrar valores nos pontos do gráfico')
    parser.add_argument('--estilo', '-e', choices=['linha', 'area'], default='linha',
                       help='Estilo do gráfico: linha ou area (padrão: linha)')
    
    args = parser.parse_args()
    
    try:
        print("Carregando dados...")
        clientes_df, faturas_df = carregar_dados()
        
        print(f"Identificando top {args.clientes} clientes...")
        top_clientes = identificar_top_clientes(clientes_df, args.clientes)
        
        print("Preparando dados de evolução mensal...")
        evolucao_mensal = preparar_dados_evolucao(top_clientes, faturas_df)
        
        print("Criando gráfico personalizado...")
        fig = criar_grafico_personalizado(evolucao_mensal, top_clientes, 
                                        args.titulo, args.valores, args.estilo)
        
        # Salvar o gráfico
        nome_arquivo = f"evolucao_top{args.clientes}_clientes_{args.estilo}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
        print(f"Gráfico salvo como: {nome_arquivo}")
        
        # Fechar a figura para liberar memória
        plt.close(fig)
        
        print(f"\nScript executado com sucesso!")
        
    except Exception as e:
        print(f"Erro durante a execução: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
