#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Streamlit para visualização dos dados de clientes LegalOne
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime

# Configuração da página
st.set_page_config(
    page_title="Dashboard LegalOne - Análise de Clientes",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

@st.cache_data
def carregar_dados_novos():
    """
    Carrega os dados do novo pipeline unificado
    """
    try:
        df = pd.read_csv('data/metricas_clientes_novo.csv')

        # Converter datas de string para datetime
        if 'data_ultima' in df.columns:
            df['data_ultima'] = pd.to_datetime(df['data_ultima'], format='%d/%m/%Y', errors='coerce')
        if 'data_primeira' in df.columns:
            df['data_primeira'] = pd.to_datetime(df['data_primeira'], format='%d/%m/%Y', errors='coerce')

        return df
    except FileNotFoundError:
        return None
    except Exception as e:
        st.error(f"❌ Erro ao carregar dados novos: {str(e)}")
        return None

@st.cache_data
def carregar_dados_antigos():
    """
    Carrega os dados do formato anterior
    """
    try:
        df = pd.read_excel('data/clientes_consolidado_completo.xlsx')
        return df
    except FileNotFoundError:
        return None
    except Exception as e:
        st.error(f"❌ Erro ao carregar dados antigos: {str(e)}")
        return None

def verificar_dados_disponiveis():
    """
    Verifica quais tipos de dados estão disponíveis
    """
    dados_novos_ok = carregar_dados_novos() is not None
    dados_antigos_ok = carregar_dados_antigos() is not None

    return dados_novos_ok, dados_antigos_ok

def criar_categoria_recencia(meses):
    """
    Cria categorias de recência baseadas nos meses desde última movimentação
    """
    if pd.isna(meses):
        return 'Sem Data'
    elif meses <= 3:
        return 'Muito Recente (≤ 3 meses)'
    elif meses <= 6:
        return 'Recente (4-6 meses)'
    elif meses <= 12:
        return 'Moderado (7-12 meses)'
    elif meses <= 24:
        return 'Antigo (13-24 meses)'
    else:
        return 'Muito Antigo (> 24 meses)'

def detectar_formato_dados(df):
    """
    Detecta se os dados são do formato antigo ou novo e mapeia as colunas
    """
    # Mapeamento de colunas: novo -> antigo (para compatibilidade)
    if 'nome_original' in df.columns:
        # Formato novo
        mapeamento = {
            'nome_original': 'Nome',
            'cpf_cnpj': 'CPF_CNPJ',
            'meses_desde_ultima': 'Meses_Desde_Ultima_Fatura',
            'data_ultima': 'Data_Ultima_Fatura',
            'data_primeira': 'Data_Primeira_Fatura',
            'valor_total_faturado': 'Valor_Total_Faturado',  # Corrigido para usar apenas valor realizado
            'total_movimentacoes': 'Total_Faturas_Unicas',
            'valor_medio': 'Valor_Medio_Por_Fatura',
            'telefones': 'Telefones',
            'emails': 'Emails',
            'area': 'Area'
        }

        # Renomear colunas para compatibilidade
        df_compat = df.rename(columns=mapeamento)

        # Criar coluna Tipo_Destinatario baseada no CPF/CNPJ
        if 'CPF_CNPJ' in df_compat.columns:
            df_compat['Tipo_Destinatario'] = df_compat['CPF_CNPJ'].apply(
                lambda x: 'PJ' if pd.notna(x) and len(str(x).replace('.', '').replace('/', '').replace('-', '')) == 14
                         else 'PF' if pd.notna(x) and len(str(x).replace('.', '').replace('/', '').replace('-', '')) == 11
                         else 'N/A'
            )

        # Criar colunas faltantes com valores padrão
        if 'UF' not in df_compat.columns:
            df_compat['UF'] = 'N/A'
        if 'CNAE' not in df_compat.columns:
            df_compat['CNAE'] = 'N/A'
        if 'Descricao_CNAE' not in df_compat.columns:
            df_compat['Descricao_CNAE'] = 'N/A'
        if 'Regime_Tributario' not in df_compat.columns:
            df_compat['Regime_Tributario'] = 'N/A'

        return df_compat, True
    else:
        # Formato antigo - retornar como está
        return df, False

def main():
    """
    Função principal da aplicação Streamlit
    """
    
    # Cabeçalho da aplicação
    st.title("📊 Dashboard LegalOne - Análise de Clientes")
    st.markdown("""
    ### Análise Completa da Base de Clientes
    
    Este dashboard apresenta uma análise detalhada dos clientes baseada em:
    - **Dados de faturamento** (valores, frequência, recência)
    - **Informações cadastrais** (CPF/CNPJ, contatos, localização)
    - **Classificação PF/PJ** e dados complementares
    
    ---
    """)
    
    # Verificar dados disponíveis
    dados_novos_ok, dados_antigos_ok = verificar_dados_disponiveis()

    if not dados_novos_ok and not dados_antigos_ok:
        st.error("❌ Nenhum arquivo de dados encontrado!")
        st.info("Execute primeiro:")
        st.code("python pipeline_unificado.py  # Para dados atualizados")
        st.info("OU os scripts antigos:")
        st.code("""python limpar_dados.py
python calcular_metricas.py
python consolidar_dados_completos.py""")
        return

    # Seleção do tipo de dados
    st.subheader("📊 Seleção da Base de Dados")

    opcoes_dados = []
    if dados_novos_ok:
        opcoes_dados.append("🆕 Dados Novos (Pipeline Unificado)")
    if dados_antigos_ok:
        opcoes_dados.append("📋 Dados Antigos (Formato Anterior)")

    if len(opcoes_dados) > 1:
        tipo_dados = st.selectbox(
            "Escolha a base de dados para análise:",
            opcoes_dados,
            help="Dados novos: processados pelo pipeline unificado com arquivo dados_legalone_novo.xlsx\nDados antigos: formato anterior com múltiplos arquivos"
        )
        usar_dados_novos = "🆕 Dados Novos" in tipo_dados
    else:
        # Se só tem uma opção, usar automaticamente
        usar_dados_novos = dados_novos_ok
        if usar_dados_novos:
            st.info("✅ Usando dados do novo pipeline unificado")
        else:
            st.info("ℹ️ Usando dados do formato anterior")

    # Carregar dados baseado na seleção
    if usar_dados_novos:
        df_original = carregar_dados_novos()
        st.success("✅ Dados do novo pipeline carregados com sucesso!")
        st.caption("📁 Fonte: metricas_clientes_novo.csv")
    else:
        df_original = carregar_dados_antigos()
        st.info("ℹ️ Dados do formato anterior carregados")
        st.caption("📁 Fonte: clientes_consolidado_completo.xlsx")

    if df_original is None:
        st.error("❌ Erro ao carregar os dados selecionados")
        return

    # Detectar formato e compatibilizar
    df, is_new_format = detectar_formato_dados(df_original)

    # Mostrar informações sobre os dados carregados
    col_info1, col_info2, col_info3 = st.columns(3)
    with col_info1:
        st.metric("📊 Total de Clientes", f"{len(df):,}")
    with col_info2:
        valor_total = df['Valor_Total_Faturado'].sum() if 'Valor_Total_Faturado' in df.columns else 0
        st.metric("💰 Valor Total", f"R$ {valor_total:,.2f}")
    with col_info3:
        if usar_dados_novos:
            st.metric("📅 Fonte", "Pipeline Novo")
        else:
            st.metric("📅 Fonte", "Formato Anterior")

    # Criar categoria de recência
    df['Categoria_Recencia'] = df['Meses_Desde_Ultima_Fatura'].apply(criar_categoria_recencia)

    st.markdown("---")

    # Sidebar com filtros
    st.sidebar.header("🔍 Filtros")

    # Mostrar informação sobre a fonte dos dados na sidebar
    if usar_dados_novos:
        st.sidebar.success("🆕 Usando Dados Novos")
        st.sidebar.caption("Pipeline Unificado")
    else:
        st.sidebar.info("📋 Usando Dados Antigos")
        st.sidebar.caption("Formato Anterior")

    # Filtro por período de emissão de notas fiscais
    st.sidebar.subheader("📅 Período de Atividade do Cliente")
    st.sidebar.caption("Filtra clientes que tiveram atividade (primeira à última NF) no período selecionado")

    # Obter datas mínima e máxima dos dados (com tratamento para valores nulos)
    datas_primeira_validas = df['Data_Primeira_Fatura'].dropna()
    datas_ultima_validas = df['Data_Ultima_Fatura'].dropna()

    if len(datas_primeira_validas) > 0 and len(datas_ultima_validas) > 0:
        data_min = datas_primeira_validas.min().date()
        data_max = datas_ultima_validas.max().date()
    else:
        # Fallback para datas padrão se não houver dados válidos
        from datetime import date
        data_min = date(2020, 1, 1)
        data_max = date.today()

    # Campos de data
    col_data1, col_data2 = st.sidebar.columns(2)
    with col_data1:
        data_inicio = st.date_input(
            "Data Início:",
            value=data_min,
            min_value=data_min,
            max_value=data_max
        )
    with col_data2:
        data_fim = st.date_input(
            "Data Fim:",
            value=data_max,
            min_value=data_min,
            max_value=data_max
        )

    # Filtro por meses desde última emissão
    st.sidebar.subheader("⏰ Recência")
    meses_validos = df['Meses_Desde_Ultima_Fatura'].dropna()
    max_meses = int(meses_validos.max()) + 1 if len(meses_validos) > 0 else 100

    meses_minimos = st.sidebar.number_input(
        "Mínimo de meses desde última movimentação:",
        min_value=0,
        max_value=max_meses,
        value=0,
        step=1,
        help="Mostra clientes com PELO MENOS este número de meses desde a última movimentação"
    )

    # Filtro por valor médio por movimentação
    st.sidebar.subheader("💰 Valor Médio por Movimentação")
    valores_medios_validos = df['Valor_Medio_Por_Fatura'].dropna()

    if len(valores_medios_validos) > 0:
        valor_medio_min = float(valores_medios_validos.min())
        valor_medio_max = float(valores_medios_validos.max())

        valor_medio_range = st.sidebar.slider(
            "Intervalo de Valor Médio (R$):",
            min_value=valor_medio_min,
            max_value=valor_medio_max,
            value=(valor_medio_min, valor_medio_max),
            step=100.0,
            format="R$ %.0f"
        )
    else:
        valor_medio_range = (0.0, 100000.0)

    # Outros filtros em expander (aba recolhível)
    with st.sidebar.expander("🏷️ Outros Filtros", expanded=False):
        # Filtro por tipo de destinatário
        tipos_validos = df['Tipo_Destinatario'].dropna().unique()
        tipos_disponiveis = ['Todos'] + sorted([t for t in tipos_validos if str(t) != 'nan'])
        tipo_selecionado = st.selectbox("Tipo de Cliente:", tipos_disponiveis)

        # Filtro por UF (se disponível)
        if 'UF' in df.columns:
            ufs_validas = df[df['UF'].notna() & (df['UF'] != '') & (df['UF'] != 'N/A')]['UF'].unique()
            ufs_disponiveis = ['Todas'] + sorted([uf for uf in ufs_validas if isinstance(uf, str)])
            uf_selecionada = st.selectbox("Estado (UF):", ufs_disponiveis)
        else:
            uf_selecionada = 'Todas'

        # Filtro por valor total mínimo
        valores_totais_validos = df['Valor_Total_Faturado'].dropna()
        max_valor_total = float(valores_totais_validos.max()) if len(valores_totais_validos) > 0 else 1000000.0

        valor_min = st.number_input(
            "Valor Total Mínimo (R$):",
            min_value=0.0,
            max_value=max_valor_total,
            value=0.0,
            step=1000.0
        )
    
    # Aplicar filtros
    df_filtrado = df.copy()

    # Filtro por período de emissão (apenas se houver datas válidas)
    if len(datas_primeira_validas) > 0 and len(datas_ultima_validas) > 0:
        data_inicio_pd = pd.to_datetime(data_inicio)
        data_fim_pd = pd.to_datetime(data_fim)

        # Cliente deve ter pelo menos uma movimentação no período selecionado
        mask_periodo = (
            (df_filtrado['Data_Primeira_Fatura'].notna()) &
            (df_filtrado['Data_Ultima_Fatura'].notna()) &
            (df_filtrado['Data_Primeira_Fatura'] <= data_fim_pd) &
            (df_filtrado['Data_Ultima_Fatura'] >= data_inicio_pd)
        )
        df_filtrado = df_filtrado[mask_periodo]

    # Filtro por meses desde última movimentação
    if meses_minimos > 0:
        df_filtrado = df_filtrado[
            (df_filtrado['Meses_Desde_Ultima_Fatura'].notna()) &
            (df_filtrado['Meses_Desde_Ultima_Fatura'] >= meses_minimos)
        ]

    # Filtro por valor médio
    if len(valores_medios_validos) > 0:
        df_filtrado = df_filtrado[
            (df_filtrado['Valor_Medio_Por_Fatura'].notna()) &
            (df_filtrado['Valor_Medio_Por_Fatura'] >= valor_medio_range[0]) &
            (df_filtrado['Valor_Medio_Por_Fatura'] <= valor_medio_range[1])
        ]

    # Outros filtros
    if tipo_selecionado != 'Todos':
        df_filtrado = df_filtrado[df_filtrado['Tipo_Destinatario'] == tipo_selecionado]

    if uf_selecionada != 'Todas' and 'UF' in df_filtrado.columns:
        df_filtrado = df_filtrado[df_filtrado['UF'] == uf_selecionada]

    if valor_min > 0:
        df_filtrado = df_filtrado[
            (df_filtrado['Valor_Total_Faturado'].notna()) &
            (df_filtrado['Valor_Total_Faturado'] >= valor_min)
        ]
    
    # Mostrar informações sobre filtros aplicados
    if len(df_filtrado) != len(df):
        st.info(f"🔍 **Filtros aplicados:** Exibindo {len(df_filtrado):,} de {len(df):,} clientes ({(len(df_filtrado)/len(df)*100):.1f}%)")

    # Tabela com dados principais (movida para o topo)
    st.subheader("📋 Dados Detalhados dos Clientes")

    # Seletor de colunas para exibir
    colunas_disponiveis = [
        'Nome', 'Tipo_Destinatario', 'CPF_CNPJ', 'UF',
        'Valor_Total_Faturado', 'Total_Faturas_Unicas', 'Valor_Medio_Por_Fatura',
        'Meses_Desde_Ultima_Fatura', 'Data_Ultima_Fatura', 'Data_Primeira_Fatura',
        'Telefones', 'Emails', 'CNAE', 'Descricao_CNAE', 'Regime_Tributario'
    ]

    colunas_selecionadas = st.multiselect(
        "Selecione as colunas para exibir:",
        colunas_disponiveis,
        default=['Nome', 'Tipo_Destinatario', 'UF', 'Valor_Total_Faturado', 'Total_Faturas_Unicas', 'Meses_Desde_Ultima_Fatura', 'Data_Ultima_Fatura', 'Valor_Medio_Por_Fatura']
    )

    if colunas_selecionadas:
        # Primeiro ordenar o DataFrame completo, depois selecionar colunas
        if 'Valor_Total_Faturado' in colunas_selecionadas:
            # Se valor total está selecionado, ordenar por ele
            df_ordenado = df_filtrado.sort_values('Valor_Total_Faturado', ascending=False)
            df_exibicao = df_ordenado[colunas_selecionadas]
        elif 'Total_Faturas_Unicas' in colunas_selecionadas:
            # Se não tem valor total mas tem total de faturas, ordenar por faturas
            df_ordenado = df_filtrado.sort_values('Total_Faturas_Unicas', ascending=False)
            df_exibicao = df_ordenado[colunas_selecionadas]
        elif 'Nome' in colunas_selecionadas:
            # Se não tem nem valor nem faturas, ordenar por nome
            df_ordenado = df_filtrado.sort_values('Nome', ascending=True)
            df_exibicao = df_ordenado[colunas_selecionadas]
        else:
            # Se não tem nenhuma das colunas principais, ordenar por valor total (sempre disponível) e depois selecionar
            df_ordenado = df_filtrado.sort_values('Valor_Total_Faturado', ascending=False)
            df_exibicao = df_ordenado[colunas_selecionadas]

        # Configurar formatação das colunas monetárias para exibição (mantendo dados numéricos para ordenação)
        df_exibicao = df_exibicao.copy()  # Evitar warning do pandas

        # Configurar formatação das colunas
        column_config = {}
        if 'Valor_Total_Faturado' in colunas_selecionadas:
            column_config['Valor_Total_Faturado'] = st.column_config.NumberColumn(
                "Valor Total Faturado",
                format="R$ %.2f"
            )
        if 'Valor_Medio_Por_Fatura' in colunas_selecionadas:
            column_config['Valor_Medio_Por_Fatura'] = st.column_config.NumberColumn(
                "Valor Médio Por Fatura",
                format="R$ %.2f"
            )
        if 'Data_Ultima_Fatura' in colunas_selecionadas:
            column_config['Data_Ultima_Fatura'] = st.column_config.DateColumn(
                "Data Última Fatura",
                format="DD/MM/YYYY"
            )
        if 'Data_Primeira_Fatura' in colunas_selecionadas:
            column_config['Data_Primeira_Fatura'] = st.column_config.DateColumn(
                "Data Primeira Fatura",
                format="DD/MM/YYYY"
            )

        st.dataframe(df_exibicao, use_container_width=True, height=400, column_config=column_config)

        # Botão para download (formatar valores monetários e datas para o CSV)
        df_download = df_exibicao.copy()
        if 'Valor_Total_Faturado' in colunas_selecionadas:
            df_download['Valor_Total_Faturado'] = df_download['Valor_Total_Faturado'].apply(lambda x: f"R$ {x:,.2f}")
        if 'Valor_Medio_Por_Fatura' in colunas_selecionadas:
            df_download['Valor_Medio_Por_Fatura'] = df_download['Valor_Medio_Por_Fatura'].apply(lambda x: f"R$ {x:,.2f}")
        if 'Data_Ultima_Fatura' in colunas_selecionadas:
            df_download['Data_Ultima_Fatura'] = df_download['Data_Ultima_Fatura'].dt.strftime('%d/%m/%Y')
        if 'Data_Primeira_Fatura' in colunas_selecionadas:
            df_download['Data_Primeira_Fatura'] = df_download['Data_Primeira_Fatura'].dt.strftime('%d/%m/%Y')

        csv = df_download.to_csv(index=False)
        st.download_button(
            label="📥 Baixar dados filtrados (CSV)",
            data=csv,
            file_name=f"clientes_legalone_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    st.markdown("---")
    
    # Layout em duas colunas para gráficos
    col_esq, col_dir = st.columns(2)
    
    with col_esq:
        # Gráfico 1: Distribuição por tempo desde última compra
        st.subheader("⏰ Distribuição por Recência")
        
        recencia_counts = df_filtrado['Categoria_Recencia'].value_counts()
        
        fig_recencia = px.pie(
            values=recencia_counts.values,
            names=recencia_counts.index,
            title="Clientes por Tempo desde Última Fatura",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        fig_recencia.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig_recencia, use_container_width=True)
        
        # Tabela de detalhes da recência
        st.write("**Detalhes por Categoria:**")
        recencia_df = pd.DataFrame({
            'Categoria': recencia_counts.index,
            'Quantidade': recencia_counts.values,
            'Percentual': (recencia_counts.values / len(df_filtrado) * 100).round(1)
        })
        st.dataframe(recencia_df, use_container_width=True)
    
    with col_dir:
        # Gráfico 2: Distribuição PF vs PJ
        st.subheader("👥 Distribuição PF vs PJ")
        
        tipo_counts = df_filtrado['Tipo_Destinatario'].value_counts()
        
        fig_tipo = px.bar(
            x=tipo_counts.index,
            y=tipo_counts.values,
            title="Quantidade de Clientes por Tipo",
            labels={'x': 'Tipo de Cliente', 'y': 'Quantidade'},
            color=tipo_counts.index,
            color_discrete_sequence=px.colors.qualitative.Pastel
        )
        fig_tipo.update_layout(showlegend=False)
        st.plotly_chart(fig_tipo, use_container_width=True)
        
        # Tabela de detalhes por tipo
        st.write("**Detalhes por Tipo:**")
        tipo_df = pd.DataFrame({
            'Tipo': tipo_counts.index,
            'Quantidade': tipo_counts.values,
            'Percentual': (tipo_counts.values / len(df_filtrado) * 100).round(1)
        })
        st.dataframe(tipo_df, use_container_width=True)
    
    # Gráfico 3: Top 10 clientes por valor (largura completa)
    st.subheader("🏆 Top 10 Clientes por Valor Total")
    
    top_10 = df_filtrado.nlargest(10, 'Valor_Total_Faturado')
    
    fig_top10 = px.bar(
        top_10,
        x='Valor_Total_Faturado',
        y='Nome',
        orientation='h',
        title="Maiores Clientes por Valor Faturado",
        labels={'Valor_Total_Faturado': 'Valor Total (R$)', 'Nome': 'Cliente'},
        color='Valor_Total_Faturado',
        color_continuous_scale='Blues'
    )
    fig_top10.update_layout(
        height=500,
        yaxis={'categoryorder': 'total ascending'}
    )
    st.plotly_chart(fig_top10, use_container_width=True)

    # Rodapé
    st.markdown("---")
    st.markdown("""
    **Dashboard LegalOne** | Análise de Clientes  
    *Dados atualizados em: {}*
    """.format(datetime.now().strftime("%d/%m/%Y às %H:%M")))

if __name__ == "__main__":
    main()
