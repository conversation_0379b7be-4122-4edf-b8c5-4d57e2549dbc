#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Interface Streamlit para visualização dos dados de clientes LegalOne
"""

import streamlit as st
import pandas as pd
import plotly.express as px
from datetime import datetime

# Configuração da página
st.set_page_config(
    page_title="Dashboard LegalOne - Análise de Clientes",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

@st.cache_data
def carregar_dados():
    """
    Carrega os dados consolidados dos clientes
    """
    try:
        df = pd.read_excel('data/clientes_consolidado_completo.xlsx')
        return df
    except FileNotFoundError:
        st.error("❌ Arquivo 'clientes_consolidado_completo.xlsx' não encontrado na pasta 'data'!")
        st.info("Execute primeiro os scripts de processamento dos dados.")
        return None
    except Exception as e:
        st.error(f"❌ Erro ao carregar dados: {str(e)}")
        return None

def criar_categoria_recencia(meses):
    """
    Cria categorias de recência baseadas nos meses desde última fatura
    """
    if meses <= 3:
        return 'Muito Recente (≤ 3 meses)'
    elif meses <= 6:
        return 'Recente (4-6 meses)'
    elif meses <= 12:
        return 'Moderado (7-12 meses)'
    elif meses <= 24:
        return 'Antigo (13-24 meses)'
    else:
        return 'Muito Antigo (> 24 meses)'

def main():
    """
    Função principal da aplicação Streamlit
    """
    
    # Cabeçalho da aplicação
    st.title("📊 Dashboard LegalOne - Análise de Clientes")
    st.markdown("""
    ### Análise Completa da Base de Clientes
    
    Este dashboard apresenta uma análise detalhada dos clientes baseada em:
    - **Dados de faturamento** (valores, frequência, recência)
    - **Informações cadastrais** (CPF/CNPJ, contatos, localização)
    - **Classificação PF/PJ** e dados complementares
    
    ---
    """)
    
    # Carregar dados
    df = carregar_dados()
    if df is None:
        return
    
    # Criar categoria de recência
    df['Categoria_Recencia'] = df['Meses_Desde_Ultima_Fatura'].apply(criar_categoria_recencia)
    
    # Sidebar com filtros
    st.sidebar.header("🔍 Filtros")

    # Filtro por período de emissão de notas fiscais
    st.sidebar.subheader("📅 Período de Atividade do Cliente")
    st.sidebar.caption("Filtra clientes que tiveram atividade (primeira à última NF) no período selecionado")

    # Obter datas mínima e máxima dos dados
    data_min = df['Data_Primeira_Fatura'].min().date()
    data_max = df['Data_Ultima_Fatura'].max().date()

    # Campos de data
    col_data1, col_data2 = st.sidebar.columns(2)
    with col_data1:
        data_inicio = st.date_input(
            "Data Início:",
            value=data_min,
            min_value=data_min,
            max_value=data_max
        )
    with col_data2:
        data_fim = st.date_input(
            "Data Fim:",
            value=data_max,
            min_value=data_min,
            max_value=data_max
        )

    # Filtro por meses desde última emissão
    st.sidebar.subheader("⏰ Recência")
    meses_minimos = st.sidebar.number_input(
        "Mínimo de meses desde última NF:",
        min_value=0,
        max_value=int(df['Meses_Desde_Ultima_Fatura'].max()) + 1,
        value=0,
        step=1,
        help="Mostra clientes com PELO MENOS este número de meses desde a última nota fiscal"
    )

    # Filtro por valor médio por fatura
    st.sidebar.subheader("💰 Valor Médio por Fatura")
    valor_medio_min = df['Valor_Medio_Por_Fatura'].min()
    valor_medio_max = df['Valor_Medio_Por_Fatura'].max()

    valor_medio_range = st.sidebar.slider(
        "Intervalo de Valor Médio (R$):",
        min_value=float(valor_medio_min),
        max_value=float(valor_medio_max),
        value=(float(valor_medio_min), float(valor_medio_max)),
        step=100.0,
        format="R$ %.0f"
    )

    # Outros filtros em expander (aba recolhível)
    with st.sidebar.expander("🏷️ Outros Filtros", expanded=False):
        # Filtro por tipo de destinatário
        tipos_disponiveis = ['Todos'] + sorted(df['Tipo_Destinatario'].unique().tolist())
        tipo_selecionado = st.selectbox("Tipo de Cliente:", tipos_disponiveis)

        # Filtro por UF
        ufs_validas = df[df['UF'].notna() & (df['UF'] != '')]['UF'].unique()
        ufs_disponiveis = ['Todas'] + sorted([uf for uf in ufs_validas if isinstance(uf, str)])
        uf_selecionada = st.selectbox("Estado (UF):", ufs_disponiveis)

        # Filtro por valor total mínimo
        valor_min = st.number_input(
            "Valor Total Mínimo (R$):",
            min_value=0.0,
            max_value=float(df['Valor_Total_Faturado'].max()),
            value=0.0,
            step=1000.0
        )
    
    # Aplicar filtros
    df_filtrado = df.copy()

    # Filtro por período de emissão
    # Verifica se há sobreposição entre o período do cliente e o período selecionado
    data_inicio_pd = pd.to_datetime(data_inicio)
    data_fim_pd = pd.to_datetime(data_fim)

    # Cliente deve ter pelo menos uma nota fiscal no período selecionado
    # Isso significa que o período do cliente deve se sobrepor ao período selecionado
    mask_periodo = (
        (df_filtrado['Data_Primeira_Fatura'] <= data_fim_pd) &
        (df_filtrado['Data_Ultima_Fatura'] >= data_inicio_pd)
    )
    df_filtrado = df_filtrado[mask_periodo]

    # Filtro por meses desde última emissão
    if meses_minimos > 0:
        df_filtrado = df_filtrado[df_filtrado['Meses_Desde_Ultima_Fatura'] >= meses_minimos]

    # Filtro por valor médio por fatura
    df_filtrado = df_filtrado[
        (df_filtrado['Valor_Medio_Por_Fatura'] >= valor_medio_range[0]) &
        (df_filtrado['Valor_Medio_Por_Fatura'] <= valor_medio_range[1])
    ]

    # Outros filtros
    if tipo_selecionado != 'Todos':
        df_filtrado = df_filtrado[df_filtrado['Tipo_Destinatario'] == tipo_selecionado]

    if uf_selecionada != 'Todas':
        df_filtrado = df_filtrado[df_filtrado['UF'] == uf_selecionada]

    if valor_min > 0:
        df_filtrado = df_filtrado[df_filtrado['Valor_Total_Faturado'] >= valor_min]
    
    # Mostrar informações sobre filtros aplicados
    if len(df_filtrado) != len(df):
        st.info(f"🔍 **Filtros aplicados:** Exibindo {len(df_filtrado):,} de {len(df):,} clientes ({(len(df_filtrado)/len(df)*100):.1f}%)")

    # Tabela com dados principais (movida para o topo)
    st.subheader("📋 Dados Detalhados dos Clientes")

    # Seletor de colunas para exibir
    colunas_disponiveis = [
        'Nome', 'Tipo_Destinatario', 'CPF_CNPJ', 'UF',
        'Valor_Total_Faturado', 'Total_Faturas_Unicas', 'Valor_Medio_Por_Fatura',
        'Meses_Desde_Ultima_Fatura', 'Data_Ultima_Fatura', 'Data_Primeira_Fatura',
        'Telefones', 'Emails', 'CNAE', 'Descricao_CNAE', 'Regime_Tributario'
    ]

    colunas_selecionadas = st.multiselect(
        "Selecione as colunas para exibir:",
        colunas_disponiveis,
        default=['Nome', 'Tipo_Destinatario', 'UF', 'Valor_Total_Faturado', 'Total_Faturas_Unicas', 'Meses_Desde_Ultima_Fatura', 'Data_Ultima_Fatura', 'Valor_Medio_Por_Fatura']
    )

    if colunas_selecionadas:
        # Primeiro ordenar o DataFrame completo, depois selecionar colunas
        if 'Valor_Total_Faturado' in colunas_selecionadas:
            # Se valor total está selecionado, ordenar por ele
            df_ordenado = df_filtrado.sort_values('Valor_Total_Faturado', ascending=False)
            df_exibicao = df_ordenado[colunas_selecionadas]
        elif 'Total_Faturas_Unicas' in colunas_selecionadas:
            # Se não tem valor total mas tem total de faturas, ordenar por faturas
            df_ordenado = df_filtrado.sort_values('Total_Faturas_Unicas', ascending=False)
            df_exibicao = df_ordenado[colunas_selecionadas]
        elif 'Nome' in colunas_selecionadas:
            # Se não tem nem valor nem faturas, ordenar por nome
            df_ordenado = df_filtrado.sort_values('Nome', ascending=True)
            df_exibicao = df_ordenado[colunas_selecionadas]
        else:
            # Se não tem nenhuma das colunas principais, ordenar por valor total (sempre disponível) e depois selecionar
            df_ordenado = df_filtrado.sort_values('Valor_Total_Faturado', ascending=False)
            df_exibicao = df_ordenado[colunas_selecionadas]

        # Configurar formatação das colunas monetárias para exibição (mantendo dados numéricos para ordenação)
        df_exibicao = df_exibicao.copy()  # Evitar warning do pandas

        # Configurar formatação das colunas
        column_config = {}
        if 'Valor_Total_Faturado' in colunas_selecionadas:
            column_config['Valor_Total_Faturado'] = st.column_config.NumberColumn(
                "Valor Total Faturado",
                format="R$ %.2f"
            )
        if 'Valor_Medio_Por_Fatura' in colunas_selecionadas:
            column_config['Valor_Medio_Por_Fatura'] = st.column_config.NumberColumn(
                "Valor Médio Por Fatura",
                format="R$ %.2f"
            )
        if 'Data_Ultima_Fatura' in colunas_selecionadas:
            column_config['Data_Ultima_Fatura'] = st.column_config.DateColumn(
                "Data Última Fatura",
                format="DD/MM/YYYY"
            )
        if 'Data_Primeira_Fatura' in colunas_selecionadas:
            column_config['Data_Primeira_Fatura'] = st.column_config.DateColumn(
                "Data Primeira Fatura",
                format="DD/MM/YYYY"
            )

        st.dataframe(df_exibicao, use_container_width=True, height=400, column_config=column_config)

        # Botão para download (formatar valores monetários e datas para o CSV)
        df_download = df_exibicao.copy()
        if 'Valor_Total_Faturado' in colunas_selecionadas:
            df_download['Valor_Total_Faturado'] = df_download['Valor_Total_Faturado'].apply(lambda x: f"R$ {x:,.2f}")
        if 'Valor_Medio_Por_Fatura' in colunas_selecionadas:
            df_download['Valor_Medio_Por_Fatura'] = df_download['Valor_Medio_Por_Fatura'].apply(lambda x: f"R$ {x:,.2f}")
        if 'Data_Ultima_Fatura' in colunas_selecionadas:
            df_download['Data_Ultima_Fatura'] = df_download['Data_Ultima_Fatura'].dt.strftime('%d/%m/%Y')
        if 'Data_Primeira_Fatura' in colunas_selecionadas:
            df_download['Data_Primeira_Fatura'] = df_download['Data_Primeira_Fatura'].dt.strftime('%d/%m/%Y')

        csv = df_download.to_csv(index=False)
        st.download_button(
            label="📥 Baixar dados filtrados (CSV)",
            data=csv,
            file_name=f"clientes_legalone_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

    st.markdown("---")
    
    # Layout em duas colunas para gráficos
    col_esq, col_dir = st.columns(2)
    
    with col_esq:
        # Gráfico 1: Distribuição por tempo desde última compra
        st.subheader("⏰ Distribuição por Recência")
        
        recencia_counts = df_filtrado['Categoria_Recencia'].value_counts()
        
        fig_recencia = px.pie(
            values=recencia_counts.values,
            names=recencia_counts.index,
            title="Clientes por Tempo desde Última Fatura",
            color_discrete_sequence=px.colors.qualitative.Set3
        )
        fig_recencia.update_traces(textposition='inside', textinfo='percent+label')
        st.plotly_chart(fig_recencia, use_container_width=True)
        
        # Tabela de detalhes da recência
        st.write("**Detalhes por Categoria:**")
        recencia_df = pd.DataFrame({
            'Categoria': recencia_counts.index,
            'Quantidade': recencia_counts.values,
            'Percentual': (recencia_counts.values / len(df_filtrado) * 100).round(1)
        })
        st.dataframe(recencia_df, use_container_width=True)
    
    with col_dir:
        # Gráfico 2: Distribuição PF vs PJ
        st.subheader("👥 Distribuição PF vs PJ")
        
        tipo_counts = df_filtrado['Tipo_Destinatario'].value_counts()
        
        fig_tipo = px.bar(
            x=tipo_counts.index,
            y=tipo_counts.values,
            title="Quantidade de Clientes por Tipo",
            labels={'x': 'Tipo de Cliente', 'y': 'Quantidade'},
            color=tipo_counts.index,
            color_discrete_sequence=px.colors.qualitative.Pastel
        )
        fig_tipo.update_layout(showlegend=False)
        st.plotly_chart(fig_tipo, use_container_width=True)
        
        # Tabela de detalhes por tipo
        st.write("**Detalhes por Tipo:**")
        tipo_df = pd.DataFrame({
            'Tipo': tipo_counts.index,
            'Quantidade': tipo_counts.values,
            'Percentual': (tipo_counts.values / len(df_filtrado) * 100).round(1)
        })
        st.dataframe(tipo_df, use_container_width=True)
    
    # Gráfico 3: Top 10 clientes por valor (largura completa)
    st.subheader("🏆 Top 10 Clientes por Valor Total")
    
    top_10 = df_filtrado.nlargest(10, 'Valor_Total_Faturado')
    
    fig_top10 = px.bar(
        top_10,
        x='Valor_Total_Faturado',
        y='Nome',
        orientation='h',
        title="Maiores Clientes por Valor Faturado",
        labels={'Valor_Total_Faturado': 'Valor Total (R$)', 'Nome': 'Cliente'},
        color='Valor_Total_Faturado',
        color_continuous_scale='Blues'
    )
    fig_top10.update_layout(
        height=500,
        yaxis={'categoryorder': 'total ascending'}
    )
    st.plotly_chart(fig_top10, use_container_width=True)

    # Rodapé
    st.markdown("---")
    st.markdown("""
    **Dashboard LegalOne** | Análise de Clientes  
    *Dados atualizados em: {}*
    """.format(datetime.now().strftime("%d/%m/%Y às %H:%M")))

if __name__ == "__main__":
    main()
