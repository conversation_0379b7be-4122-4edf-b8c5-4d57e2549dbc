#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para consolidar dados completos dos clientes
- Métricas calculadas
- Dados de contato (telefones, emails)
- Informações complementares (CNAE, regime tributário, UF)
- CPF/CNPJ
- Todos os 308 destinatários

Autor: Análise de Dados LegalOne
Data: 2025-01-07
"""

import pandas as pd
import numpy as np
from pathlib import Path

def carregar_todos_dados():
    """
    Carrega todos os dados necessários
    """
    print("📂 CARREGANDO TODOS OS DADOS")
    print("="*50)
    
    pasta_data = Path('data')
    
    try:
        # Carregar métricas calculadas
        df_metricas = pd.read_excel(pasta_data / 'metricas_destinatarios.xlsx')
        print(f"✅ Métricas: {len(df_metricas)} destinatários")
        
        # Carregar dados limpos das bases
        df_pf = pd.read_excel(pasta_data / 'pf_limpa.xlsx')
        df_pj = pd.read_excel(pasta_data / 'pj_limpa.xlsx')
        print(f"✅ PF: {len(df_pf)} registros")
        print(f"✅ PJ: {len(df_pj)} registros")
        
        return df_metricas, df_pf, df_pj
        
    except Exception as e:
        print(f"❌ Erro ao carregar dados: {str(e)}")
        return None, None, None

def normalizar_texto_busca(texto):
    """
    Normaliza texto para busca (mesmo padrão usado na limpeza)
    """
    if pd.isna(texto) or texto == '':
        return None
    
    import unicodedata
    import re
    
    texto = str(texto)
    # Remover acentos
    texto = unicodedata.normalize('NFD', texto)
    texto = ''.join(char for char in texto if unicodedata.category(char) != 'Mn')
    # Padronizar espaços e maiúsculas
    texto = re.sub(r'\s+', ' ', texto.strip().upper())
    
    return texto

def buscar_dados_pf(nome_destinatario, df_pf):
    """
    Busca dados completos de uma pessoa física
    """
    nome_norm = normalizar_texto_busca(nome_destinatario)
    
    # Buscar correspondência exata
    match = df_pf[df_pf['Nome_Normalizado'] == nome_norm]
    
    if len(match) == 0:
        # Buscar correspondência parcial
        for _, row in df_pf.iterrows():
            if pd.notna(row['Nome_Normalizado']):
                if nome_norm in row['Nome_Normalizado'] or row['Nome_Normalizado'] in nome_norm:
                    match = df_pf[df_pf.index == row.name]
                    break
    
    if len(match) > 0:
        # Pegar o primeiro match (ou consolidar se houver múltiplos)
        dados = match.iloc[0]
        
        # Consolidar telefones (filtrar 'Sim' e 'Não')
        telefones = []
        if pd.notna(dados.get('Telefones / Principal')):
            tel = str(dados['Telefones / Principal']).strip()
            if tel.lower() not in ['sim', 'não', 'nao']:
                telefones.append(tel)
        if pd.notna(dados.get('Telefones / Número')):
            tel = str(dados['Telefones / Número']).strip()
            if tel.lower() not in ['sim', 'não', 'nao']:
                telefones.append(tel)

        # Consolidar emails (filtrar 'Sim' e 'Não')
        emails = []
        if pd.notna(dados.get('E-mails / E-mail')):
            email = str(dados['E-mails / E-mail']).strip()
            if email.lower() not in ['sim', 'não', 'nao']:
                emails.append(email)
        if pd.notna(dados.get('E-mails / Cobrança')):
            email = str(dados['E-mails / Cobrança']).strip()
            if email.lower() not in ['sim', 'não', 'nao']:
                emails.append(email)
        if pd.notna(dados.get('E-mails / Faturamento')):
            email = str(dados['E-mails / Faturamento']).strip()
            if email.lower() not in ['sim', 'não', 'nao']:
                emails.append(email)
        
        # Consolidar endereço
        endereco_partes = []
        if pd.notna(dados.get('Endereços / Logradouro')):
            endereco_partes.append(str(dados['Endereços / Logradouro']))
        if pd.notna(dados.get('Endereços / Número')):
            endereco_partes.append(str(dados['Endereços / Número']))
        if pd.notna(dados.get('Endereços / Complemento')):
            endereco_partes.append(str(dados['Endereços / Complemento']))
        
        return {
            'Nome_Original': dados.get('Nome', ''),
            'CPF_CNPJ': dados.get('CPF_Limpo', ''),
            'Telefones': '; '.join(set(telefones)) if telefones else '',
            'Emails': '; '.join(set(emails)) if emails else '',
            'UF': dados.get('Endereços / UF', ''),
            'CNAE': '',  # PF não tem CNAE
            'Regime_Tributario': ''  # PF não tem regime tributário
        }
    
    return None

def buscar_dados_pj(razao_social, df_pj):
    """
    Busca dados completos de uma pessoa jurídica
    """


    razao_norm = normalizar_texto_busca(razao_social)


    
    # Buscar correspondência exata
    match = df_pj[df_pj['Razao_Social_Normalizada'] == razao_norm]



    if len(match) == 0:
        # Buscar correspondência parcial
        for _, row in df_pj.iterrows():
            if pd.notna(row['Razao_Social_Normalizada']):
                if razao_norm in row['Razao_Social_Normalizada'] or row['Razao_Social_Normalizada'] in razao_norm:
                    match = df_pj[df_pj.index == row.name]
                    break
    
    if len(match) > 0:
        # Se houver múltiplos registros, consolidar informações
        # Consolidar telefones de todos os registros
        telefones = set()
        emails = set()
        enderecos = set()
        ceps = set()
        cidades = set()
        ufs = set()
        cnaes = set()
        
        for _, row in match.iterrows():
            # Telefones (filtrar 'Sim' e 'Não')
            if pd.notna(row.get('Telefones / Principal')):
                tel = str(row['Telefones / Principal']).strip()
                if tel.lower() not in ['sim', 'não', 'nao']:
                    telefones.add(tel)
            if pd.notna(row.get('Telefones / Número')):
                tel = str(row['Telefones / Número']).strip()
                if tel.lower() not in ['sim', 'não', 'nao']:
                    telefones.add(tel)
            if pd.notna(row.get('Telefones / Principal.1')):
                tel = str(row['Telefones / Principal.1']).strip()
                if tel.lower() not in ['sim', 'não', 'nao']:
                    telefones.add(tel)

            # Emails (filtrar 'Sim' e 'Não')
            if pd.notna(row.get('E-mails / E-mail')):
                email = str(row['E-mails / E-mail']).strip()
                if email.lower() not in ['sim', 'não', 'nao']:
                    emails.add(email)
            if pd.notna(row.get('E-mails / Cobrança')):
                email = str(row['E-mails / Cobrança']).strip()
                if email.lower() not in ['sim', 'não', 'nao']:
                    emails.add(email)
            if pd.notna(row.get('E-mails / Faturamento')):
                email = str(row['E-mails / Faturamento']).strip()
                if email.lower() not in ['sim', 'não', 'nao']:
                    emails.add(email)
            if pd.notna(row.get('Contatos / E-mails / E-mail')):
                email = str(row['Contatos / E-mails / E-mail']).strip()
                if email.lower() not in ['sim', 'não', 'nao']:
                    emails.add(email)
            
            # Endereços
            endereco_partes = []
            if pd.notna(row.get('Endereços / Logradouro')):
                endereco_partes.append(str(row['Endereços / Logradouro']))
            if pd.notna(row.get('Endereços / Número')):
                endereco_partes.append(str(row['Endereços / Número']))
            if endereco_partes:
                enderecos.add(', '.join(endereco_partes))
            
            # CEPs, Cidades, UFs
            if pd.notna(row.get('Endereços / CEP')):
                ceps.add(str(row['Endereços / CEP']))
            if pd.notna(row.get('Endereços / Cidade')):
                cidades.add(str(row['Endereços / Cidade']))
            if pd.notna(row.get('Endereços / UF')):
                ufs.add(str(row['Endereços / UF']))
            if pd.notna(row.get('Contatos / Endereços / UF')):
                ufs.add(str(row['Contatos / Endereços / UF']))
            
            # CNAEs
            if pd.notna(row.get('CNAEs / Código')):
                cnaes.add(str(row['CNAEs / Código']))
        
        # Pegar dados principais do primeiro registro
        primeiro = match.iloc[0]
        
        resultado = {
            'Nome_Original': primeiro.get('Razão social', ''),
            'CPF_CNPJ': primeiro.get('CNPJ_Limpo', ''),
            'Telefones': '; '.join(telefones) if telefones else '',
            'Emails': '; '.join(emails) if emails else '',
            'UF': '; '.join(ufs) if ufs else '',
            'CNAE': '; '.join(cnaes) if cnaes else '',
            'Regime_Tributario': primeiro.get('Regime_Tributario_Padronizado', '')
        }

        return resultado
    
    return None

def consolidar_dados_completos(df_metricas, df_pf, df_pj):
    """
    Consolida todos os dados em um único DataFrame
    """
    print(f"\n🔄 CONSOLIDANDO DADOS COMPLETOS")
    print("="*60)

    dados_consolidados = []

    total = len(df_metricas)
    for i, (_, row) in enumerate(df_metricas.iterrows(), 1):
        if i % 50 == 0 or i == total:
            print(f"   Processando {i}/{total} destinatários...")

        destinatario = row['Destinatario']
        tipo = row['Tipo_Destinatario']
        
        # Buscar dados complementares baseado no tipo
        dados_complementares = None



        if 'PF' in tipo:
            dados_complementares = buscar_dados_pf(destinatario, df_pf)
        elif 'PJ' in tipo:
            dados_complementares = buscar_dados_pj(destinatario, df_pj)
        
        # Montar registro consolidado
        registro = {
            # Dados básicos
            'Nome': dados_complementares['Nome_Original'] if dados_complementares else destinatario,
            'CPF_CNPJ': dados_complementares['CPF_CNPJ'] if dados_complementares else '',
            'Tipo_Destinatario': tipo,

            # Métricas temporais
            'Meses_Desde_Ultima_Fatura': round(row['Meses_Desde_Ultima_Fatura'], 2),
            'Data_Ultima_Fatura': row['Data_Ultima_Fatura'],
            'Data_Primeira_Fatura': row['Data_Primeira_Fatura'],
            'Periodo_Cliente_Meses': round(row['Periodo_Cliente_Meses'], 2),

            # Métricas de volume e valor (formatadas com 2 casas decimais)
            'Total_Faturas_Unicas': row['Total_Faturas_Unicas'],
            'Valor_Total_Faturado': round(row['Valor_Total_Faturado'], 2),
            'Valor_Medio_Por_Fatura': round(row['Valor_Medio_Por_Fatura'], 2),
            'Valor_Minimo': round(row['Valor_Minimo'], 2),
            'Valor_Maximo': round(row['Valor_Maximo'], 2),
            'Frequencia_Faturas_Por_Mes': round(row['Frequencia_Faturas_Por_Mes'], 2),

            # Dados de contato
            'Telefones': dados_complementares['Telefones'] if dados_complementares else '',
            'Emails': dados_complementares['Emails'] if dados_complementares else '',
            'UF': dados_complementares['UF'] if dados_complementares else '',

            # Dados específicos PJ
            'CNAE': dados_complementares['CNAE'] if dados_complementares else '',
            'Regime_Tributario': dados_complementares['Regime_Tributario'] if dados_complementares else ''
        }
        
        dados_consolidados.append(registro)
    
    df_consolidado = pd.DataFrame(dados_consolidados)
    
    print(f"✅ Dados consolidados: {len(df_consolidado)} registros")
    
    return df_consolidado

def gerar_estatisticas_consolidacao(df_consolidado):
    """
    Gera estatísticas da consolidação
    """
    print(f"\n📊 ESTATÍSTICAS DA CONSOLIDAÇÃO")
    print("="*50)

    total = len(df_consolidado)

    # Dados de contato encontrados
    com_cpf_cnpj = df_consolidado['CPF_CNPJ'].notna().sum()
    com_telefone = (df_consolidado['Telefones'] != '').sum()
    com_email = (df_consolidado['Emails'] != '').sum()
    com_uf = (df_consolidado['UF'] != '').sum()
    com_cnae = (df_consolidado['CNAE'] != '').sum()

    print(f"📋 DADOS ENCONTRADOS:")
    print(f"   • CPF/CNPJ: {com_cpf_cnpj}/{total} ({com_cpf_cnpj/total*100:.1f}%)")
    print(f"   • Telefones: {com_telefone}/{total} ({com_telefone/total*100:.1f}%)")
    print(f"   • Emails: {com_email}/{total} ({com_email/total*100:.1f}%)")
    print(f"   • UF: {com_uf}/{total} ({com_uf/total*100:.1f}%)")
    print(f"   • CNAE: {com_cnae}/{total} ({com_cnae/total*100:.1f}%)")

    # Distribuição por UF
    print(f"\n🗺️  DISTRIBUIÇÃO POR UF:")
    ufs = df_consolidado[df_consolidado['UF'] != '']['UF'].value_counts().head(10)
    for uf, count in ufs.items():
        print(f"   • {uf}: {count} clientes")

def main():
    """
    Função principal
    """
    print("🚀 CONSOLIDANDO DADOS COMPLETOS DOS CLIENTES")
    print("="*80)
    
    # Carregar todos os dados
    df_metricas, df_pf, df_pj = carregar_todos_dados()
    if df_metricas is None:
        return
    
    # Consolidar dados
    df_consolidado = consolidar_dados_completos(df_metricas, df_pf, df_pj)
    
    # Gerar estatísticas
    gerar_estatisticas_consolidacao(df_consolidado)
    
    # Salvar arquivo final
    print(f"\n💾 SALVANDO ARQUIVO CONSOLIDADO")
    print("="*50)
    
    try:
        arquivo_final = 'data/clientes_consolidado_completo.xlsx'
        
        # Ordenar por Valor Total Faturado (maior para menor)
        df_final = df_consolidado.sort_values('Valor_Total_Faturado', ascending=False)
        
        df_final.to_excel(arquivo_final, index=False)
        
        print(f"✅ Arquivo salvo: {arquivo_final}")
        print(f"   📊 {len(df_final)} clientes com dados completos")
        print(f"   📋 {len(df_final.columns)} colunas de informações")
        
    except Exception as e:
        print(f"❌ Erro ao salvar: {str(e)}")
    
    print(f"\n{'='*80}")
    print("✅ CONSOLIDAÇÃO CONCLUÍDA!")
    print("="*80)
    
    return df_consolidado

if __name__ == "__main__":
    df_consolidado = main()
