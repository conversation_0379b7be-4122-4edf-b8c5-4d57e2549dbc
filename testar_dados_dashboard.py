#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar se ambos os tipos de dados estão funcionando no dashboard
"""

import pandas as pd
import os

def testar_dados():
    """
    Testa se os dados novos e antigos estão disponíveis e funcionando
    """
    print("🔍 TESTANDO DISPONIBILIDADE DOS DADOS")
    print("="*60)
    
    # Testar dados novos
    print("\n📊 DADOS NOVOS (Pipeline Unificado):")
    try:
        if os.path.exists('data/metricas_clientes_novo.csv'):
            df_novo = pd.read_csv('data/metricas_clientes_novo.csv')
            print(f"✅ Arquivo encontrado: metricas_clientes_novo.csv")
            print(f"   📋 {len(df_novo)} clientes")
            print(f"   💰 Valor total: R$ {df_novo['valor_total'].sum():,.2f}")
            print(f"   📅 Colunas: {len(df_novo.columns)}")
            
            # Verificar colunas principais
            colunas_esperadas = ['nome_original', 'cpf_cnpj', 'valor_total', 'meses_desde_ultima']
            colunas_ok = all(col in df_novo.columns for col in colunas_esperadas)
            if colunas_ok:
                print("   ✅ Estrutura de colunas OK")
            else:
                print("   ⚠️ Algumas colunas esperadas não encontradas")
        else:
            print("❌ Arquivo não encontrado: metricas_clientes_novo.csv")
            print("   💡 Execute: python pipeline_unificado.py")
    except Exception as e:
        print(f"❌ Erro ao carregar dados novos: {str(e)}")
    
    # Testar dados antigos
    print("\n📋 DADOS ANTIGOS (Formato Anterior):")
    try:
        if os.path.exists('data/clientes_consolidado_completo.xlsx'):
            df_antigo = pd.read_excel('data/clientes_consolidado_completo.xlsx')
            print(f"✅ Arquivo encontrado: clientes_consolidado_completo.xlsx")
            print(f"   📋 {len(df_antigo)} clientes")
            print(f"   💰 Valor total: R$ {df_antigo['Valor_Total_Faturado'].sum():,.2f}")
            print(f"   📅 Colunas: {len(df_antigo.columns)}")
            
            # Verificar colunas principais
            colunas_esperadas = ['Nome', 'CPF_CNPJ', 'Valor_Total_Faturado', 'Meses_Desde_Ultima_Fatura']
            colunas_ok = all(col in df_antigo.columns for col in colunas_esperadas)
            if colunas_ok:
                print("   ✅ Estrutura de colunas OK")
            else:
                print("   ⚠️ Algumas colunas esperadas não encontradas")
        else:
            print("❌ Arquivo não encontrado: clientes_consolidado_completo.xlsx")
            print("   💡 Execute os scripts antigos de processamento")
    except Exception as e:
        print(f"❌ Erro ao carregar dados antigos: {str(e)}")
    
    print(f"\n{'='*60}")
    print("✅ TESTE CONCLUÍDO!")
    print("\n💡 COMO USAR NO DASHBOARD:")
    print("1. Abra o dashboard: streamlit run app_streamlit.py")
    print("2. Na seção 'Seleção da Base de Dados', escolha:")
    print("   • 🆕 Dados Novos - para usar o pipeline unificado")
    print("   • 📋 Dados Antigos - para usar o formato anterior")
    print("="*60)

if __name__ == "__main__":
    testar_dados()
