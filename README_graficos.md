# Scripts de Gráficos de Evolução de Clientes

Este conjunto de scripts permite gerar gráficos da evolução mensal dos clientes com maior faturamento total.

## Arquivos

### 1. `grafico_evolucao_top5_clientes.py`
Script básico que gera um gráfico dos 5 clientes com maior faturamento total.

**Uso:**
```bash
python grafico_evolucao_top5_clientes.py
```

**Saída:**
- Gráfico PNG com evolução mensal dos top 5 clientes
- Relatório no console com informações detalhadas

### 2. `grafico_evolucao_personalizado.py`
Script avançado com opções de personalização.

**Uso básico:**
```bash
python grafico_evolucao_personalizado.py
```

**Opções disponíveis:**
```bash
python grafico_evolucao_personalizado.py --help

# Exemplos:
# Top 10 clientes
python grafico_evolucao_personalizado.py --clientes 10

# Com título personalizado
python grafico_evolucao_personalizado.py --titulo "Evolução dos Principais Clientes - 2024"

# Mostrar valores nos pontos
python grafico_evolucao_personalizado.py --valores

# Gráfico de área
python grafico_evolucao_personalizado.py --estilo area

# Combinando opções
python grafico_evolucao_personalizado.py -n 8 -t "Top 8 Clientes" -v -e area
```

## Parâmetros do Script Personalizado

| Parâmetro | Abreviação | Descrição | Padrão |
|-----------|------------|-----------|---------|
| `--clientes` | `-n` | Número de top clientes a exibir | 5 |
| `--titulo` | `-t` | Título personalizado para o gráfico | Automático |
| `--valores` | `-v` | Mostrar valores nos pontos | Desabilitado |
| `--estilo` | `-e` | Estilo do gráfico (linha/area) | linha |

## Dados Utilizados

### Sheet1 (Informações dos Clientes)
- **Nome**: Nome do cliente
- **Valor_Total_Faturado**: Valor total faturado (usado para ranking)
- **CPF_CNPJ**: Identificação do cliente

### Sheet2 (Faturas Mensais)
- **Nome**: Nome do cliente
- **Data_Fatura**: Data da fatura (formato dd/mm/yyyy)
- **Valor_Fatura**: Valor da fatura individual

## Funcionalidades

### Script Básico
1. ✅ Identifica os 5 clientes com maior valor total
2. ✅ Extrai dados mensais de faturamento
3. ✅ Gera gráfico de linha com evolução temporal
4. ✅ Salva gráfico em PNG de alta qualidade
5. ✅ Exibe relatório resumo no console

### Script Personalizado
1. ✅ Número configurável de clientes (top N)
2. ✅ Título personalizado
3. ✅ Opção de mostrar valores nos pontos
4. ✅ Dois estilos de gráfico (linha e área)
5. ✅ Interface de linha de comando
6. ✅ Nomes de arquivo automáticos com timestamp

## Requisitos

```
pandas>=1.5.0
matplotlib>=3.7.0
seaborn>=0.12.0
openpyxl>=3.1.0
numpy>=1.24.0
```

## Instalação das Dependências

```bash
pip install pandas matplotlib seaborn openpyxl numpy
```

## Estrutura dos Arquivos de Saída

### Gráficos Gerados
- **Nome**: `evolucao_top5_clientes_YYYYMMDD_HHMMSS.png` (script básico)
- **Nome**: `evolucao_topN_clientes_estilo_YYYYMMDD_HHMMSS.png` (script personalizado)
- **Resolução**: 300 DPI (alta qualidade)
- **Formato**: PNG com fundo transparente

### Relatório Console
```
=== TOP 5 CLIENTES POR VALOR TOTAL FATURADO ===
1. Cliente A
   Valor Total: R$ 1,411,170.60
   CPF/CNPJ: 7605506000173.0

=== RELATÓRIO RESUMO ===
Cliente A:
  - Valor total no período: R$ 1,214,070.60
  - Valor médio mensal: R$ 29,611.48
  - Número de meses com faturamento: 41
```

## Características dos Gráficos

### Elementos Visuais
- **Linhas**: Espessura 2.5-3px com marcadores circulares
- **Cores**: Paleta automática diferenciada para cada cliente
- **Grid**: Linhas de grade sutis para facilitar leitura
- **Legenda**: Posicionada à direita do gráfico
- **Eixos**: Formatação em reais (R$) no eixo Y

### Formatação
- **Título**: Fonte grande e negrito
- **Labels**: Eixos rotacionados para melhor legibilidade
- **Layout**: Ajuste automático para evitar sobreposição
- **Estilo**: Baseado no tema Seaborn para aparência profissional

## Tratamento de Dados

### Conversão de Datas
- Formato de entrada: dd/mm/yyyy (brasileiro)
- Agrupamento: Por mês/ano para análise temporal
- Tratamento de erros: Datas inválidas são ignoradas

### Agregação
- **Método**: Soma dos valores por cliente por mês
- **Ordenação**: Cronológica para visualização temporal
- **Filtros**: Apenas clientes presentes em ambas as sheets

## Solução de Problemas

### Erro de Data
Se aparecer erro relacionado ao formato de data, verifique se as datas na Sheet2 estão no formato dd/mm/yyyy.

### Arquivo não Encontrado
Certifique-se de que o arquivo `data/clientes_legalone_fatura.xlsx` existe no diretório correto.

### Dependências
Execute `pip install -r requirements.txt` para instalar todas as dependências necessárias.
