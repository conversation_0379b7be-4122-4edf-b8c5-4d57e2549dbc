#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para gerar gráfico da evolução mensal dos 5 clientes com maior faturamento total
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import numpy as np

def carregar_dados():
    """Carrega os dados das duas sheets do arquivo Excel"""
    file_path = 'data/clientes_legalone_fatura.xlsx'
    
    # Carregar dados dos clientes (Sheet1)
    clientes_df = pd.read_excel(file_path, sheet_name='Sheet1')
    
    # Carregar dados das faturas mensais (Sheet2)
    faturas_df = pd.read_excel(file_path, sheet_name='Sheet2')
    
    return clientes_df, faturas_df

def identificar_top5_clientes(clientes_df):
    """Identifica os 5 clientes com maior valor total de faturamento"""
    # Ordenar por valor total faturado (decrescente) e pegar os top 5
    top5 = clientes_df.nlargest(5, 'Valor_Total_Faturado')
    
    print("=== TOP 5 CLIENTES POR VALOR TOTAL FATURADO ===")
    for i, (idx, cliente) in enumerate(top5.iterrows(), 1):
        print(f"{i}. {cliente['Nome']}")
        print(f"   Valor Total: R$ {cliente['Valor_Total_Faturado']:,.2f}")
        print(f"   CPF/CNPJ: {cliente['CPF_CNPJ']}")
        print()
    
    return top5

def preparar_dados_evolucao(top5_clientes, faturas_df):
    """Prepara os dados de evolução mensal para os top 5 clientes"""
    # Converter coluna de data para datetime (formato brasileiro dd/mm/yyyy)
    faturas_df['Data_Fatura'] = pd.to_datetime(faturas_df['Data_Fatura'], format='%d/%m/%Y', errors='coerce')
    
    # Criar coluna de ano-mês para agrupamento
    faturas_df['Ano_Mes'] = faturas_df['Data_Fatura'].dt.to_period('M')
    
    # Filtrar faturas apenas dos top 5 clientes
    top5_nomes = top5_clientes['Nome'].tolist()
    faturas_top5 = faturas_df[faturas_df['Nome'].isin(top5_nomes)].copy()
    
    # Agrupar por cliente e mês, somando os valores
    evolucao_mensal = faturas_top5.groupby(['Nome', 'Ano_Mes'])['Valor_Fatura'].sum().reset_index()
    
    # Converter período de volta para datetime para facilitar o plot
    evolucao_mensal['Data'] = evolucao_mensal['Ano_Mes'].dt.to_timestamp()
    
    return evolucao_mensal

def criar_grafico(evolucao_mensal, top5_clientes):
    """Cria o gráfico de evolução mensal"""
    # Configurar o estilo
    plt.style.use('seaborn-v0_8')
    fig, ax = plt.subplots(figsize=(15, 8))
    
    # Definir cores para cada cliente
    cores = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    # Plotar linha para cada cliente
    for i, (idx, cliente) in enumerate(top5_clientes.iterrows()):
        nome_cliente = cliente['Nome']
        dados_cliente = evolucao_mensal[evolucao_mensal['Nome'] == nome_cliente]
        
        if not dados_cliente.empty:
            # Ordenar por data
            dados_cliente = dados_cliente.sort_values('Data')
            
            ax.plot(dados_cliente['Data'], 
                   dados_cliente['Valor_Fatura'], 
                   marker='o', 
                   linewidth=2.5,
                   markersize=6,
                   color=cores[i],
                   label=f"{nome_cliente[:30]}..." if len(nome_cliente) > 30 else nome_cliente)
    
    # Configurar o gráfico
    ax.set_title('Evolução Mensal do Faturamento - Top 5 Clientes', 
                fontsize=16, fontweight='bold', pad=20)
    ax.set_xlabel('Período', fontsize=12, fontweight='bold')
    ax.set_ylabel('Valor Faturado (R$)', fontsize=12, fontweight='bold')
    
    # Formatar eixo Y para mostrar valores em reais
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'R$ {x:,.0f}'))
    
    # Rotacionar labels do eixo X
    plt.xticks(rotation=45)
    
    # Configurar legenda
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
    
    # Adicionar grid
    ax.grid(True, alpha=0.3)
    
    # Ajustar layout
    plt.tight_layout()
    
    return fig

def gerar_relatorio_resumo(top5_clientes, evolucao_mensal):
    """Gera um relatório resumo dos dados"""
    print("\n=== RELATÓRIO RESUMO ===")
    
    for idx, cliente in top5_clientes.iterrows():
        nome = cliente['Nome']
        dados_cliente = evolucao_mensal[evolucao_mensal['Nome'] == nome]
        
        if not dados_cliente.empty:
            valor_total = dados_cliente['Valor_Fatura'].sum()
            valor_medio = dados_cliente['Valor_Fatura'].mean()
            num_meses = len(dados_cliente)
            
            print(f"\n{nome}:")
            print(f"  - Valor total no período: R$ {valor_total:,.2f}")
            print(f"  - Valor médio mensal: R$ {valor_medio:,.2f}")
            print(f"  - Número de meses com faturamento: {num_meses}")

def main():
    """Função principal"""
    try:
        print("Carregando dados...")
        clientes_df, faturas_df = carregar_dados()
        
        print("Identificando top 5 clientes...")
        top5_clientes = identificar_top5_clientes(clientes_df)
        
        print("Preparando dados de evolução mensal...")
        evolucao_mensal = preparar_dados_evolucao(top5_clientes, faturas_df)
        
        print("Criando gráfico...")
        fig = criar_grafico(evolucao_mensal, top5_clientes)
        
        # Salvar o gráfico
        nome_arquivo = f"evolucao_top5_clientes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        plt.savefig(nome_arquivo, dpi=300, bbox_inches='tight')
        print(f"Gráfico salvo como: {nome_arquivo}")

        # Fechar a figura para liberar memória
        plt.close(fig)
        
        # Gerar relatório resumo
        gerar_relatorio_resumo(top5_clientes, evolucao_mensal)
        
        print(f"\nScript executado com sucesso!")
        
    except Exception as e:
        print(f"Erro durante a execução: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
