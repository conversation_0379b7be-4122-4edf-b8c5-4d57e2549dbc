#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para verificar se a correção do Periodo_Cliente_Meses funcionou
"""

import pandas as pd

def main():
    # Carregar dados consolidados
    df = pd.read_excel('data/clientes_consolidado_completo.xlsx')

    print('=== VERIFICAÇÃO DA CORREÇÃO ===')
    print(f'Total de clientes: {len(df)}')

    # Verificar se ainda há clientes com 0 meses
    clientes_zero_meses = df[df['Periodo_Cliente_Meses'] <= 0]
    print(f'Clientes com 0 ou menos meses: {len(clientes_zero_meses)}')

    # Mostrar estatísticas do período
    print(f'\nEstatísticas do Periodo_Cliente_Meses:')
    print(f'Mínimo: {df["Periodo_Cliente_Meses"].min():.1f} meses')
    print(f'Máximo: {df["Periodo_Cliente_Meses"].max():.1f} meses')
    print(f'Média: {df["Periodo_Cliente_Meses"].mean():.1f} meses')
    print(f'Mediana: {df["Periodo_Cliente_Meses"].median():.1f} meses')

    # Mostrar alguns exemplos de clientes com apenas 1 fatura
    clientes_1_fatura = df[df['Total_Faturas_Unicas'] == 1]
    print(f'\nClientes com apenas 1 fatura: {len(clientes_1_fatura)}')
    if len(clientes_1_fatura) > 0:
        print('Exemplos (primeiros 3):')
        for i, (_, row) in enumerate(clientes_1_fatura.head(3).iterrows()):
            print(f'  {i+1}. {row["Nome"][:40]}...')
            print(f'     Período: {row["Periodo_Cliente_Meses"]:.1f} meses')
            print(f'     Faturas: {row["Total_Faturas_Unicas"]}')
            print(f'     Primeira fatura: {row["Data_Primeira_Fatura"]}')
            print(f'     Última fatura: {row["Data_Ultima_Fatura"]}')
            print()

    # Verificar se a lógica está correta para clientes com múltiplas faturas
    print('\n=== VERIFICAÇÃO PARA CLIENTES COM MÚLTIPLAS FATURAS ===')
    clientes_multiplas = df[df['Total_Faturas_Unicas'] > 1].head(3)
    for i, (_, row) in enumerate(clientes_multiplas.iterrows()):
        primeira = pd.to_datetime(row["Data_Primeira_Fatura"])
        ultima = pd.to_datetime(row["Data_Ultima_Fatura"])
        dias_diferenca = (ultima - primeira).days
        meses_calculados_antigo = round(dias_diferenca / 30.44, 1)  # Lógica antiga
        meses_calculados_novo = meses_calculados_antigo + 1  # Lógica nova
        
        print(f'{i+1}. {row["Nome"][:40]}...')
        print(f'   Faturas: {row["Total_Faturas_Unicas"]}')
        print(f'   Primeira: {primeira.strftime("%d/%m/%Y")}')
        print(f'   Última: {ultima.strftime("%d/%m/%Y")}')
        print(f'   Dias diferença: {dias_diferenca}')
        print(f'   Lógica antiga: {meses_calculados_antigo:.1f} meses')
        print(f'   Lógica nova: {meses_calculados_novo:.1f} meses')
        print(f'   Valor no arquivo: {row["Periodo_Cliente_Meses"]:.1f} meses')
        print(f'   ✅ Correção aplicada: {"Sim" if abs(row["Periodo_Cliente_Meses"] - meses_calculados_novo) < 0.1 else "Não"}')
        print()

if __name__ == "__main__":
    main()
