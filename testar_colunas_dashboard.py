#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script para testar as colunas disponíveis em cada formato de dados
"""

import pandas as pd
import os

def testar_colunas():
    """
    Testa as colunas disponíveis em cada formato de dados
    """
    print("🔍 TESTANDO COLUNAS DISPONÍVEIS POR FORMATO")
    print("="*70)
    
    # Testar dados novos
    print("\n🆕 DADOS NOVOS (Pipeline Unificado):")
    print("-" * 50)
    try:
        if os.path.exists('data/metricas_clientes_novo.csv'):
            df_novo = pd.read_csv('data/metricas_clientes_novo.csv')
            print(f"✅ Arquivo: metricas_clientes_novo.csv")
            print(f"📊 {len(df_novo)} clientes, {len(df_novo.columns)} colunas")
            print("\n📋 Colunas disponíveis:")
            for i, col in enumerate(df_novo.columns, 1):
                print(f"   {i:2d}. {col}")
            
            print(f"\n💰 Valores monetários:")
            colunas_valor = [col for col in df_novo.columns if 'valor' in col.lower()]
            for col in colunas_valor:
                total = df_novo[col].sum()
                print(f"   • {col}: R$ {total:,.2f}")
                
        else:
            print("❌ Arquivo não encontrado: metricas_clientes_novo.csv")
    except Exception as e:
        print(f"❌ Erro ao carregar dados novos: {str(e)}")
    
    # Testar dados antigos
    print(f"\n📋 DADOS ANTIGOS (Formato Anterior):")
    print("-" * 50)
    try:
        if os.path.exists('data/clientes_consolidado_completo.xlsx'):
            df_antigo = pd.read_excel('data/clientes_consolidado_completo.xlsx')
            print(f"✅ Arquivo: clientes_consolidado_completo.xlsx")
            print(f"📊 {len(df_antigo)} clientes, {len(df_antigo.columns)} colunas")
            print("\n📋 Colunas disponíveis:")
            for i, col in enumerate(df_antigo.columns, 1):
                print(f"   {i:2d}. {col}")
                
            print(f"\n💰 Valores monetários:")
            colunas_valor = [col for col in df_antigo.columns if 'valor' in col.lower() or 'faturado' in col.lower()]
            for col in colunas_valor:
                if col in df_antigo.columns:
                    total = df_antigo[col].sum()
                    print(f"   • {col}: R$ {total:,.2f}")
                
        else:
            print("❌ Arquivo não encontrado: clientes_consolidado_completo.xlsx")
    except Exception as e:
        print(f"❌ Erro ao carregar dados antigos: {str(e)}")
    
    # Comparação
    print(f"\n🔄 COMPARAÇÃO DE COLUNAS:")
    print("-" * 50)
    
    try:
        if os.path.exists('data/metricas_clientes_novo.csv') and os.path.exists('data/clientes_consolidado_completo.xlsx'):
            df_novo = pd.read_csv('data/metricas_clientes_novo.csv')
            df_antigo = pd.read_excel('data/clientes_consolidado_completo.xlsx')
            
            colunas_novo = set(df_novo.columns)
            colunas_antigo = set(df_antigo.columns)
            
            print("🆕 Colunas APENAS no formato novo:")
            apenas_novo = colunas_novo - colunas_antigo
            for col in sorted(apenas_novo):
                print(f"   • {col}")
            
            print(f"\n📋 Colunas APENAS no formato antigo:")
            apenas_antigo = colunas_antigo - colunas_novo
            for col in sorted(apenas_antigo):
                print(f"   • {col}")
            
            print(f"\n🔄 Colunas em AMBOS os formatos:")
            comuns = colunas_novo & colunas_antigo
            for col in sorted(comuns):
                print(f"   • {col}")
                
    except Exception as e:
        print(f"❌ Erro na comparação: {str(e)}")
    
    print(f"\n{'='*70}")
    print("✅ TESTE CONCLUÍDO!")
    print("\n💡 COMO USAR NO DASHBOARD:")
    print("1. Abra: streamlit run app_streamlit.py")
    print("2. Selecione o tipo de dados na seção 'Seleção da Base de Dados'")
    print("3. As colunas disponíveis se adaptam automaticamente ao formato escolhido")
    print("="*70)

if __name__ == "__main__":
    testar_colunas()
