# 📊 Dashboard LegalOne - Análise de Clientes

Sistema completo de análise de dados de clientes com interface web interativa.

## 🚀 Como Usar

### Opção 1: Ar<PERSON><PERSON>ch (Windows) - MAIS FÁCIL
```
Duplo clique em: abrir_dashboard.bat
```

### Opção 2: Script Python
```bash
python abrir_dashboard.py
```

### Opção 3: Comando Direto
```bash
streamlit run app_streamlit.py
```

## 📁 Estrutura do Projeto

```
📦 sol-temp-legalone/
├── 📊 data/                          # Dados
│   ├── faturas_emitidas.xlsx         # Arquivo original das faturas
│   ├── base_pf_legalone.xlsx         # Base pessoas físicas
│   ├── base_pj_legalone.xlsx         # Base pessoas jurídicas
│   └── clientes_consolidado_completo.xlsx  # Dados finais consolidados
├── 🔧 Scripts de Processamento:
│   ├── explorar_dados.py             # 1. Exploração inicial
│   ├── limpar_dados.py               # 2. Limpeza e normalização
│   ├── calcular_metricas.py          # 3. Cálculo de métricas
│   ├── consolidar_dados_completos.py # 4. Consolidação final
│   └── consolidar_duplicatas_parciais.py # 5. Remoção de duplicatas
├── 📊 Dashboard:
│   ├── app_streamlit.py              # Aplicação principal
│   ├── abrir_dashboard.py            # Script para abrir
│   └── abrir_dashboard.bat           # Arquivo batch (Windows)
└── 📋 requirements.txt               # Dependências
```

## 🔄 Pipeline Completo

Se precisar reprocessar todos os dados:

```bash
# 1. Explorar dados originais
python explorar_dados.py

# 2. Limpar e normalizar
python limpar_dados.py

# 3. Calcular métricas por cliente
python calcular_metricas.py

# 4. Consolidar dados completos
python consolidar_dados_completos.py

# 5. Remover duplicatas parciais
python consolidar_duplicatas_parciais.py

# 6. Abrir dashboard
python abrir_dashboard.py
```

## 📊 Funcionalidades do Dashboard

### 🔍 Filtros Interativos
- **Tipo de Cliente**: PF, PJ, Parciais
- **Estado (UF)**: Filtro geográfico
- **Valor Mínimo**: Filtro por faturamento

### 📈 Visualizações
- **Gráfico Pizza**: Distribuição por recência
- **Gráfico Barras**: PF vs PJ
- **Ranking**: Top 10 clientes por valor

### 📋 Tabela Interativa
- **Colunas personalizáveis**
- **Ordenação automática**
- **Download CSV**

### 📊 Métricas Principais
- Total de clientes: **295**
- Valor total faturado: **R$ 14.749.507,49**
- Total de faturas: **2.312**

## 🌐 Acesso

Após executar qualquer um dos comandos acima:
- **URL Local**: http://localhost:8501
- **O navegador abrirá automaticamente**

## ⏹️ Para Parar

Pressione `Ctrl+C` no terminal onde o dashboard está rodando.

## 🔧 Dependências

```bash
pip install streamlit pandas plotly openpyxl
```

## 📞 Suporte

Para dúvidas ou problemas:
1. Verifique se todos os arquivos estão na pasta `data/`
2. Execute o pipeline completo se necessário
3. Verifique se as dependências estão instaladas

---

**Dashboard LegalOne** | Análise de Clientes | Versão 1.0
